#!/usr/bin/env python3
"""
Medical Dental Analysis System - Web Application
Flask version of the desktop PyQt5 application
"""

import os
import cv2
import numpy as np
from flask import Flask, render_template, request, jsonify, send_file, session, redirect, url_for
from werkzeug.utils import secure_filename
from ultralytics import Y<PERSON><PERSON>
import zipfile
import tempfile
import shutil
from datetime import datetime
import base64
import io
from PIL import Image
import json

app = Flask(__name__)
app.secret_key = 'medical_dental_analysis_secret_key_2024'

# Configuration
UPLOAD_FOLDER = 'uploads'
RESULTS_FOLDER = 'results'
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'bmp', 'tiff', 'gif'}
MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB max file size

app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['RESULTS_FOLDER'] = RESULTS_FOLDER
app.config['MAX_CONTENT_LENGTH'] = MAX_CONTENT_LENGTH

# Create necessary directories
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs(RESULTS_FOLDER, exist_ok=True)

# Load YOLO model
try:
    model = YOLO("mouth_detector.pt")
    model_info = "Using trained mouth detection model"
except:
    try:
        model = YOLO("yolov8n.pt")
        model_info = "Using general YOLO model. For best results, use a trained mouth detection model."
    except Exception as e:
        model = None
        model_info = f"Could not load YOLO model: {str(e)}"

# Translations dictionary (same as desktop app)
translations = {
    "en": {
        "app_title": "Medical Dental Analysis System",
        "select_image": "Select Image",
        "process": "Analyze Image",
        "original": "Original Image",
        "result": "Processed Result",
        "cropped_result": "Cropped Result",
        "mirror_horizontal": "Mirror Left-Right",
        "process_folder": "Process Folder",
        "export_zip": "Export ZIP",
        "clear": "Clear Results",
        "language": "Language",
        "status": "Ready",
        "processing": "Processing...",
        "analysis_completed": "Analysis completed successfully",
        "no_image_selected": "Please select an image first.",
        "no_processed_image": "No processed image to export.",
        "export_success": "Image exported successfully",
        "export_failed": "Failed to export image",
        "could_not_load": "Could not load the selected image file.",
        "processing_error": "Processing Error",
        "processing_failed": "Processing failed",
        "success": "Success",
        "error": "Error",
        "warning": "Warning",
        "no_mouth_detected": "No mouth/teeth detected in the image.",
        "detected_region_empty": "Detected region is empty.",
        "processing_error_msg": "Processing error:",
        "model_info_title": "Model Info",
        "model_info_msg": "Using general YOLO model. For best results, use a trained mouth detection model.",
        "could_not_load_model": "Could not load YOLO model:",
        "batch_processing": "Batch Processing...",
        "batch_completed": "Batch processing completed",
        "images_processed": "images processed",
        "no_images_found": "No valid images found in the uploaded files.",
        "batch_export_success": "Batch results exported successfully",
        "files_exported": "files exported successfully",
        "dental_analysis_header": "🦷 DENTAL ANALYSIS",
        "upload_images": "Upload Images",
        "drag_drop_hint": "Drag and drop images here or click to select",
        "batch_results": "Batch Results",
        "download_result": "Download Result",
        "apply_mirror": "Apply Mirror",
        "previous": "Previous",
        "next": "Next",
        "image_counter": "Image {current} of {total}",
        "processing_image": "Processing image {current} of {total}...",
        "interface_cleared": "Interface cleared and reset",
        "select_folder": "Select Folder",
    },
    "it": {
        "app_title": "Sistema di Analisi Dentale Medica",
        "select_image": "Seleziona Immagine",
        "process": "Analizza Immagine",
        "original": "Immagine Originale",
        "result": "Risultato Elaborato",
        "cropped_result": "Risultato Ritagliato",
        "mirror_horizontal": "Specchia Sinistra-Destra",
        "process_folder": "Elabora Cartella",
        "export_zip": "Esporta ZIP",
        "clear": "Cancella Risultati",
        "language": "Lingua",
        "status": "Pronto",
        "processing": "Elaborazione...",
        "analysis_completed": "Analisi completata con successo",
        "no_image_selected": "Seleziona prima un'immagine.",
        "no_processed_image": "Nessuna immagine elaborata da esportare.",
        "export_success": "Immagine esportata con successo",
        "export_failed": "Impossibile esportare l'immagine",
        "could_not_load": "Impossibile caricare il file immagine selezionato.",
        "processing_error": "Errore di Elaborazione",
        "processing_failed": "Elaborazione fallita",
        "success": "Successo",
        "error": "Errore",
        "warning": "Avviso",
        "no_mouth_detected": "Nessuna bocca/denti rilevata nell'immagine.",
        "detected_region_empty": "La regione rilevata è vuota.",
        "processing_error_msg": "Errore di elaborazione:",
        "model_info_title": "Info Modello",
        "model_info_msg": "Utilizzo modello YOLO generale. Per risultati migliori, usa un modello di rilevamento bocca addestrato.",
        "could_not_load_model": "Impossibile caricare il modello YOLO:",
        "batch_processing": "Elaborazione Lotto...",
        "batch_completed": "Elaborazione lotto completata",
        "images_processed": "immagini elaborate",
        "no_images_found": "Nessuna immagine valida trovata nei file caricati.",
        "batch_export_success": "Risultati lotto esportati con successo",
        "files_exported": "file esportati con successo",
        "dental_analysis_header": "🦷 ANALISI DENTALE",
        "upload_images": "Carica Immagini",
        "drag_drop_hint": "Trascina e rilascia le immagini qui o clicca per selezionare",
        "batch_results": "Risultati Lotto",
        "download_result": "Scarica Risultato",
        "apply_mirror": "Applica Specchio",
        "previous": "Precedente",
        "next": "Successivo",
        "image_counter": "Immagine {current} di {total}",
        "processing_image": "Elaborazione immagine {current} di {total}...",
        "interface_cleared": "Interfaccia pulita e reimpostata",
        "select_folder": "Seleziona Cartella",
    }
}

def allowed_file(filename):
    """Check if file extension is allowed"""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def get_language():
    """Get current language from session"""
    return session.get('language', 'en')

def get_translations():
    """Get translations for current language"""
    return translations[get_language()]

def process_single_image(image_path):
    """Process a single image using YOLO model (same logic as desktop app)"""
    try:
        if model is None:
            return None, "Model not loaded"
            
        # Load the image
        img = cv2.imread(image_path)
        if img is None:
            return None, "Could not load image"

        # Check if image is vertical and rotate 90 degrees to right if needed
        h, w = img.shape[:2]
        if h > w:  # Image is taller than wide (vertical)
            img = cv2.rotate(img, cv2.ROTATE_90_CLOCKWISE)

        # Save image for YOLO processing (after rotation adjustment)
        temp_path = f"temp_{os.getpid()}_{os.path.basename(image_path)}"
        cv2.imwrite(temp_path, img)

        # Run YOLO model
        results = model(temp_path)

        # Clean up temporary file
        try:
            os.remove(temp_path)
        except:
            pass

        # Check if any detections were found
        if len(results[0].boxes) == 0:
            return None, "No mouth/teeth detected in the image"

        # Process detection
        for box in results[0].boxes:
            x1, y1, x2, y2 = map(int, box.xyxy[0])
            
            # Ensure coordinates are within image bounds
            x1, y1 = max(0, x1), max(0, y1)
            x2, y2 = min(img.shape[1], x2), min(img.shape[0], y2)

            roi = img[y1:y2, x1:x2]
            
            if roi.size == 0:
                return None, "Detected region is empty"

            # Return cropped region without any orientation correction
            return roi, None

    except Exception as e:
        return None, f"Processing error: {str(e)}"

def image_to_base64(image):
    """Convert OpenCV image to base64 string for web display"""
    try:
        # Convert BGR to RGB
        rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # Convert to PIL Image
        pil_image = Image.fromarray(rgb_image)
        
        # Save to bytes buffer
        buffer = io.BytesIO()
        pil_image.save(buffer, format='PNG')
        buffer.seek(0)
        
        # Encode to base64
        img_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
        return f"data:image/png;base64,{img_base64}"
    except Exception as e:
        print(f"Error converting image to base64: {str(e)}")
        return None

@app.route('/')
def index():
    """Main page"""
    tr = get_translations()
    return render_template('index.html', 
                         translations=tr, 
                         model_info=model_info,
                         current_lang=get_language())

@app.route('/set_language/<lang>')
def set_language(lang):
    """Set language preference"""
    if lang in translations:
        session['language'] = lang
    return redirect(url_for('index'))

@app.route('/upload_folder', methods=['POST'])
def upload_folder():
    """Handle folder upload and processing (batch processing)"""
    tr = get_translations()

    if 'files' not in request.files:
        return jsonify({'error': tr['no_image_selected']})

    files = request.files.getlist('files')
    if not files or all(f.filename == '' for f in files):
        return jsonify({'error': tr['no_image_selected']})

    try:
        batch_results = []
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Filter and sort files by name for consistent ordering
        valid_files = []
        for file in files:
            if file and allowed_file(file.filename):
                valid_files.append(file)

        # Sort files by filename for consistent processing order
        valid_files.sort(key=lambda x: x.filename.lower())

        if not valid_files:
            return jsonify({'error': tr['no_images_found']})

        for i, file in enumerate(valid_files):
            # Save uploaded file
            filename = secure_filename(file.filename)
            unique_filename = f"{timestamp}_{i:03d}_{filename}"
            file_path = os.path.join(app.config['UPLOAD_FOLDER'], unique_filename)
            file.save(file_path)

            # Process the image
            result_image, error = process_single_image(file_path)

            if result_image is not None:
                # Save result
                result_filename = f"result_{unique_filename}"
                result_path = os.path.join(app.config['RESULTS_FOLDER'], result_filename)
                cv2.imwrite(result_path, result_image)

                # Convert images to base64
                original_img = cv2.imread(file_path)
                original_base64 = image_to_base64(original_img)
                result_base64 = image_to_base64(result_image)

                batch_results.append({
                    'original_path': file_path,
                    'result_path': result_path,
                    'original_filename': filename,
                    'result_filename': result_filename,
                    'original_image': original_base64,
                    'result_image': result_base64,
                    'mirror_horizontal': True,  # Default mirror state
                    'index': len(batch_results)
                })

        if not batch_results:
            return jsonify({'error': tr['no_images_found']})

        # Store in session
        session['batch_results'] = batch_results
        session['current_batch_index'] = 0

        return jsonify({
            'success': True,
            'batch_results': batch_results,
            'total_processed': len(batch_results),
            'total_uploaded': len(valid_files),
            'message': f"{tr['batch_completed']}: {len(batch_results)} {tr['images_processed']}"
        })

    except Exception as e:
        return jsonify({'error': f"{tr['processing_error_msg']} {str(e)}"})

@app.route('/get_current_image')
def get_current_image():
    """Get current image in batch for navigation"""
    tr = get_translations()

    if 'batch_results' not in session or not session['batch_results']:
        return jsonify({'error': tr['no_processed_image']})

    try:
        batch_results = session['batch_results']
        current_index = session.get('current_batch_index', 0)

        if current_index >= len(batch_results):
            current_index = 0
            session['current_batch_index'] = current_index

        current_result = batch_results[current_index]

        # Apply current mirror setting
        result_img = cv2.imread(current_result['result_path'])
        if current_result.get('mirror_horizontal', True):
            result_img = cv2.flip(result_img, 1)

        result_base64 = image_to_base64(result_img)

        return jsonify({
            'success': True,
            'original_image': current_result['original_image'],
            'result_image': result_base64,
            'current_index': current_index,
            'total_images': len(batch_results),
            'filename': current_result['original_filename'],
            'mirror_horizontal': current_result.get('mirror_horizontal', True)
        })

    except Exception as e:
        return jsonify({'error': f"{tr['processing_error_msg']} {str(e)}"})

@app.route('/navigate_batch/<direction>')
def navigate_batch(direction):
    """Navigate through batch results"""
    tr = get_translations()

    if 'batch_results' not in session or not session['batch_results']:
        return jsonify({'error': tr['no_processed_image']})

    try:
        batch_results = session['batch_results']
        current_index = session.get('current_batch_index', 0)

        if direction == 'next' and current_index < len(batch_results) - 1:
            current_index += 1
        elif direction == 'prev' and current_index > 0:
            current_index -= 1

        session['current_batch_index'] = current_index

        # Return current image data
        return get_current_image()

    except Exception as e:
        return jsonify({'error': f"{tr['processing_error_msg']} {str(e)}"})

@app.route('/apply_mirror_batch', methods=['POST'])
def apply_mirror_batch():
    """Apply horizontal mirror to current batch image"""
    tr = get_translations()

    if 'batch_results' not in session or not session['batch_results']:
        return jsonify({'error': tr['no_processed_image']})

    try:
        data = request.get_json()
        mirror_horizontal = data.get('mirror_horizontal', True)

        batch_results = session['batch_results']
        current_index = session.get('current_batch_index', 0)

        if current_index >= len(batch_results):
            return jsonify({'error': tr['no_processed_image']})

        # Update mirror setting for current image
        batch_results[current_index]['mirror_horizontal'] = mirror_horizontal
        session['batch_results'] = batch_results

        # Load and apply mirror to result image
        current_result = batch_results[current_index]
        result_img = cv2.imread(current_result['result_path'])

        if mirror_horizontal:
            result_img = cv2.flip(result_img, 1)

        result_base64 = image_to_base64(result_img)

        return jsonify({
            'success': True,
            'result_image': result_base64
        })

    except Exception as e:
        return jsonify({'error': f"{tr['processing_error_msg']} {str(e)}"})

@app.route('/apply_mirror_to_all', methods=['POST'])
def apply_mirror_to_all():
    """Apply current mirror setting to all remaining images in batch"""
    tr = get_translations()

    if 'batch_results' not in session or not session['batch_results']:
        return jsonify({'error': tr['no_processed_image']})

    try:
        data = request.get_json()
        mirror_horizontal = data.get('mirror_horizontal', True)

        batch_results = session['batch_results']
        current_index = session.get('current_batch_index', 0)

        # Apply to all remaining images (from current index onwards)
        remaining_count = 0
        for i in range(current_index, len(batch_results)):
            batch_results[i]['mirror_horizontal'] = mirror_horizontal
            remaining_count += 1

        session['batch_results'] = batch_results

        return jsonify({
            'success': True,
            'applied_count': remaining_count,
            'message': f"Applied mirror setting to {remaining_count} images"
        })

    except Exception as e:
        return jsonify({'error': f"{tr['processing_error_msg']} {str(e)}"})

@app.route('/download_current')
def download_current():
    """Download current image in batch"""
    tr = get_translations()

    if 'batch_results' not in session or not session['batch_results']:
        return jsonify({'error': tr['no_processed_image']})

    try:
        batch_results = session['batch_results']
        current_index = session.get('current_batch_index', 0)

        if current_index >= len(batch_results):
            return jsonify({'error': tr['no_processed_image']})

        current_result = batch_results[current_index]

        # Load result image
        result_img = cv2.imread(current_result['result_path'])
        if result_img is None:
            return jsonify({'error': tr['could_not_load']})

        # Apply mirror if needed
        if current_result.get('mirror_horizontal', True):
            result_img = cv2.flip(result_img, 1)

        # Create temporary file for download
        temp_dir = tempfile.mkdtemp()
        download_filename = f"processed_{current_result['original_filename']}"
        download_path = os.path.join(temp_dir, download_filename)
        cv2.imwrite(download_path, result_img)

        return send_file(download_path,
                        as_attachment=True,
                        download_name=download_filename,
                        mimetype='image/png')

    except Exception as e:
        return jsonify({'error': f"{tr['export_failed']} {str(e)}"})

@app.route('/download_batch_zip')
def download_batch_zip():
    """Download batch results as ZIP file"""
    tr = get_translations()

    if 'batch_results' not in session or not session['batch_results']:
        return jsonify({'error': tr['no_processed_image']})

    try:
        batch_results = session['batch_results']

        # Create temporary directory for ZIP creation
        temp_dir = tempfile.mkdtemp()
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        zip_filename = f"dental_analysis_batch_{timestamp}.zip"
        zip_path = os.path.join(temp_dir, zip_filename)

        # Create ZIP file
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for i, result in enumerate(batch_results):
                # Load result image
                result_img = cv2.imread(result['result_path'])
                if result_img is None:
                    continue

                # Apply mirror if needed
                if result.get('mirror_horizontal', True):
                    result_img = cv2.flip(result_img, 1)

                # Create unique filename
                original_name = os.path.splitext(result['original_filename'])[0]
                export_filename = f"{original_name}_processed.png"

                # Save to temporary file
                temp_img_path = os.path.join(temp_dir, f"temp_{i}.png")
                cv2.imwrite(temp_img_path, result_img)

                # Add to ZIP
                zipf.write(temp_img_path, export_filename)

                # Clean up temporary image file
                try:
                    os.remove(temp_img_path)
                except:
                    pass

        return send_file(zip_path,
                        as_attachment=True,
                        download_name=zip_filename,
                        mimetype='application/zip')

    except Exception as e:
        return jsonify({'error': f"{tr['export_failed']} {str(e)}"})

@app.route('/clear_session')
def clear_session():
    """Clear session data and temporary files"""
    tr = get_translations()

    try:
        # Clean up batch files
        if 'batch_results' in session:
            batch_results = session['batch_results']
            for result in batch_results:
                for path_key in ['original_path', 'result_path']:
                    if path_key in result:
                        try:
                            os.remove(result[path_key])
                        except:
                            pass

        # Clear session
        session.clear()

        return jsonify({'success': True, 'message': tr.get('interface_cleared', 'Session cleared')})

    except Exception as e:
        return jsonify({'error': f"Clear session error: {str(e)}"})

if __name__ == '__main__':
    print(f"Model Info: {model_info}")
    print("Starting Medical Dental Analysis Web Application...")
    app.run(debug=True, host='0.0.0.0', port=5000)
