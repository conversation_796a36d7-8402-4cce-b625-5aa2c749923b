<!DOCTYPE html>
<html lang="{{ current_lang }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ translations.app_title }}</title>
    <style>
        /* Medical Theme CSS - Matching Desktop Application */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', 'Arial', sans-serif;
            font-size: 10pt;
            background-color: #fafcff;
            color: #34495e;
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 15px;
            min-height: 100vh;
        }

        /* Header */
        .header {
            background: linear-gradient(135deg, #ecf0f1 0%, #ffffff 100%);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            text-align: center;
            border: 2px solid #2980b9;
        }

        .header h1 {
            font-size: 24pt;
            font-weight: bold;
            color: #2980b9;
            margin-bottom: 10px;
        }

        .model-info {
            font-style: italic;
            color: #7f8c8d;
            font-size: 9pt;
        }

        /* Language Selector */
        .language-selector {
            position: absolute;
            top: 20px;
            right: 20px;
            background: white;
            border: 2px solid #2980b9;
            border-radius: 6px;
            padding: 8px;
        }

        .language-selector select {
            border: none;
            background: transparent;
            font-size: 10pt;
            color: #2980b9;
            font-weight: bold;
        }

        /* Main Layout */
        .main-layout {
            display: grid;
            grid-template-columns: 350px 1fr;
            gap: 20px;
            min-height: 600px;
        }

        /* Left Panel */
        .left-panel {
            background: white;
            border: 2px solid #2980b9;
            border-radius: 8px;
            padding: 20px;
            height: fit-content;
        }

        .panel-section {
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border: 1px solid #bdc3c7;
            border-radius: 8px;
        }

        .panel-section h3 {
            color: #2980b9;
            font-size: 12pt;
            font-weight: bold;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        /* Buttons */
        .btn {
            background-color: #2980b9;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 12px 20px;
            font-weight: bold;
            font-size: 10pt;
            min-height: 44px;
            cursor: pointer;
            width: 100%;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            background-color: #1f6c9b;
            transform: translateY(-2px);
        }

        .btn:disabled {
            background-color: #ecf0f5;
            color: gray;
            cursor: not-allowed;
            transform: none;
        }

        .btn-secondary {
            background-color: #95a5a6;
        }

        .btn-secondary:hover {
            background-color: #7f8c8d;
        }

        /* File Upload */
        .file-upload {
            border: 3px dashed #2980b9;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            background: #f8f9fa;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .file-upload:hover {
            background: #e3f2fd;
            border-color: #1f6c9b;
        }

        .file-upload.dragover {
            background: #e3f2fd;
            border-color: #1f6c9b;
        }

        .file-upload input[type="file"] {
            display: none;
        }

        .upload-text {
            color: #2980b9;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .upload-hint {
            color: #7f8c8d;
            font-size: 9pt;
            font-style: italic;
        }

        /* Checkbox */
        .checkbox-container {
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 10px 0;
        }

        .checkbox {
            width: 18px;
            height: 18px;
            border: 2px solid #2980b9;
            border-radius: 4px;
            background-color: white;
            cursor: pointer;
            position: relative;
        }

        .checkbox input {
            display: none;
        }

        .checkbox input:checked + .checkmark {
            background-color: #2980b9;
        }

        .checkbox input:checked + .checkmark::after {
            content: "✓";
            color: white;
            font-weight: bold;
            font-size: 12px;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .checkmark {
            width: 100%;
            height: 100%;
            border-radius: 2px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Right Panel */
        .right-panel {
            background: white;
            border: 2px solid #2980b9;
            border-radius: 8px;
            padding: 20px;
        }

        /* Image Display */
        .image-display {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .image-container {
            border: 3px solid #bdc3c7;
            border-radius: 10px;
            background-color: #f8f9fa;
            padding: 15px;
            text-align: center;
            min-height: 300px;
            display: flex;
            flex-direction: column;
        }

        .image-title {
            font-weight: bold;
            font-size: 12pt;
            color: #2c3e50;
            margin-bottom: 15px;
        }

        .image-content {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 250px;
        }

        .image-content img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
            border-radius: 5px;
        }

        .image-placeholder {
            color: #7f8c8d;
            font-style: italic;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
        }

        /* Batch Navigation */
        .batch-navigation {
            display: none;
            background: #f8f9fa;
            border: 1px solid #bdc3c7;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
        }

        .batch-navigation.visible {
            display: block;
        }

        .nav-controls {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 15px;
        }

        .nav-counter {
            font-weight: bold;
            font-size: 12pt;
            color: #2980b9;
            text-align: center;
            flex: 1;
        }

        .nav-btn {
            padding: 8px 16px;
            font-size: 9pt;
            min-height: 36px;
            width: auto;
            margin: 0;
        }

        /* Progress Bar */
        .progress-container {
            display: none;
            margin: 15px 0;
        }

        .progress-container.visible {
            display: block;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #ecf0f5;
            border: 2px solid #2980b9;
            border-radius: 6px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background-color: #27ae60;
            width: 0%;
            transition: width 0.3s ease;
        }

        .progress-text {
            text-align: center;
            margin-top: 5px;
            font-size: 9pt;
            color: #2980b9;
            font-weight: bold;
        }

        /* Status Messages */
        .status-message {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }

        .status-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status-info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .main-layout {
                grid-template-columns: 300px 1fr;
            }
        }

        @media (max-width: 768px) {
            .main-layout {
                grid-template-columns: 1fr;
                gap: 15px;
            }
            
            .image-display {
                grid-template-columns: 1fr;
            }
            
            .language-selector {
                position: static;
                margin-bottom: 15px;
                text-align: center;
            }
        }

        /* Loading Spinner */
        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #2980b9;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Language Selector -->
        <div class="language-selector">
            <select onchange="changeLanguage(this.value)">
                <option value="en" {% if current_lang == 'en' %}selected{% endif %}>🇺🇸 English</option>
                <option value="it" {% if current_lang == 'it' %}selected{% endif %}>🇮🇹 Italiano</option>
            </select>
        </div>

        <!-- Header -->
        <div class="header">
            <h1>{{ translations.dental_analysis_header }}</h1>
            <div class="model-info">{{ model_info }}</div>
        </div>

        <!-- Main Layout -->
        <div class="main-layout">
            <!-- Left Panel -->
            <div class="left-panel">
                <!-- Folder Upload -->
                <div class="panel-section">
                    <h3>📁 {{ translations.process_folder }}</h3>
                    <div class="file-upload" onclick="document.getElementById('folder-files').click()">
                        <input type="file" id="folder-files" accept="image/*" multiple webkitdirectory directory onchange="uploadFolder(this)">
                        <div class="upload-text">{{ translations.process_folder }}</div>
                        <div class="upload-hint">Select a folder containing images</div>
                    </div>
                    <div class="file-upload" onclick="document.getElementById('multiple-files').click()" style="margin-top: 10px;">
                        <input type="file" id="multiple-files" accept="image/*" multiple onchange="uploadMultiple(this)">
                        <div class="upload-text">Or select multiple images</div>
                        <div class="upload-hint">{{ translations.drag_drop_hint }}</div>
                    </div>
                    <button class="btn" id="process-folder" onclick="processFolder()" disabled>
                        ⚡ {{ translations.process_folder }}
                    </button>
                </div>

                <!-- Mirror Controls -->
                <div class="panel-section">
                    <h3>🪞 {{ translations.mirror_horizontal }}</h3>
                    <div class="checkbox-container">
                        <label class="checkbox">
                            <input type="checkbox" id="mirror-horizontal" checked onchange="applyMirror()">
                            <span class="checkmark"></span>
                        </label>
                        <span>{{ translations.mirror_horizontal }}</span>
                    </div>
                </div>

                <!-- Export Controls -->
                <div class="panel-section">
                    <h3>💾 Export Results</h3>
                    <button class="btn" id="download-current" onclick="downloadCurrent()" disabled>
                        💾 {{ translations.download_result }}
                    </button>
                    <button class="btn" id="download-all" onclick="downloadAll()" disabled>
                        📦 {{ translations.export_zip }}
                    </button>
                    <button class="btn" id="apply-to-all" onclick="applyToAll()" disabled>
                        🔄 Apply to All Remaining
                    </button>
                    <button class="btn btn-secondary" onclick="clearResults()">
                        🗑️ {{ translations.clear }}
                    </button>
                </div>

                <!-- Progress Bar -->
                <div class="progress-container" id="progress-container">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progress-fill"></div>
                    </div>
                    <div class="progress-text" id="progress-text">{{ translations.processing }}</div>
                </div>

                <!-- Status Messages -->
                <div id="status-messages"></div>
            </div>

            <!-- Right Panel -->
            <div class="right-panel">
                <h3>🖼️ {{ translations.images_display }}</h3>

                <!-- Image Display -->
                <div class="image-display">
                    <!-- Original Image -->
                    <div class="image-container">
                        <div class="image-title">📷 {{ translations.original }}</div>
                        <div class="image-content" id="original-image-content">
                            <div class="image-placeholder">{{ translations.select_image }}</div>
                        </div>
                    </div>

                    <!-- Result Image -->
                    <div class="image-container">
                        <div class="image-title">🦷 {{ translations.cropped_result }}</div>
                        <div class="image-content" id="result-image-content">
                            <div class="image-placeholder">{{ translations.process }}</div>
                        </div>
                    </div>
                </div>

                <!-- Batch Navigation -->
                <div class="batch-navigation" id="batch-navigation">
                    <div class="nav-controls">
                        <button class="btn nav-btn" id="prev-btn" onclick="previousImage()" disabled>
                            ◀ {{ translations.previous }}
                        </button>
                        <div class="nav-counter" id="nav-counter">1 / 1</div>
                        <button class="btn nav-btn" id="next-btn" onclick="nextImage()" disabled>
                            {{ translations.next }} ▶
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let currentFolderFiles = [];
        let batchResults = [];
        let currentBatchIndex = 0;
        let isProcessing = false;

        // Language change
        function changeLanguage(lang) {
            window.location.href = `/set_language/${lang}`;
        }

        // File upload handlers
        function uploadFolder(input) {
            if (input.files && input.files.length > 0) {
                currentFolderFiles = Array.from(input.files);
                document.getElementById('process-folder').disabled = false;
                showStatus(`${currentFolderFiles.length} images selected from folder`, 'info');

                // Show first image as preview
                if (currentFolderFiles.length > 0) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        showOriginalImage(e.target.result);
                    };
                    reader.readAsDataURL(currentFolderFiles[0]);
                }
            }
        }

        function uploadMultiple(input) {
            if (input.files && input.files.length > 0) {
                currentFolderFiles = Array.from(input.files);
                document.getElementById('process-folder').disabled = false;
                showStatus(`${currentFolderFiles.length} images selected`, 'info');

                // Show first image as preview
                if (currentFolderFiles.length > 0) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        showOriginalImage(e.target.result);
                    };
                    reader.readAsDataURL(currentFolderFiles[0]);
                }
            }
        }

        // Image display functions
        function showOriginalImage(src) {
            const content = document.getElementById('original-image-content');
            content.innerHTML = `<img src="${src}" alt="Original Image">`;
        }

        function showResultImage(src) {
            const content = document.getElementById('result-image-content');
            content.innerHTML = `<img src="${src}" alt="Result Image">`;
        }

        function clearImages() {
            document.getElementById('original-image-content').innerHTML =
                '<div class="image-placeholder">{{ translations.select_image }}</div>';
            document.getElementById('result-image-content').innerHTML =
                '<div class="image-placeholder">{{ translations.process }}</div>';
        }

        // Status message functions
        function showStatus(message, type = 'info') {
            const container = document.getElementById('status-messages');
            const statusDiv = document.createElement('div');
            statusDiv.className = `status-message status-${type}`;
            statusDiv.textContent = message;
            container.appendChild(statusDiv);

            // Auto-remove after 5 seconds
            setTimeout(() => {
                if (statusDiv.parentNode) {
                    statusDiv.parentNode.removeChild(statusDiv);
                }
            }, 5000);
        }

        // Progress bar functions
        function showProgress() {
            document.getElementById('progress-container').classList.add('visible');
        }

        function hideProgress() {
            document.getElementById('progress-container').classList.remove('visible');
        }

        function updateProgress(percent, text = '') {
            document.getElementById('progress-fill').style.width = percent + '%';
            if (text) {
                document.getElementById('progress-text').textContent = text;
            }
        }

        // Folder processing
        async function processFolder() {
            if (!currentFolderFiles.length || isProcessing) return;

            isProcessing = true;
            showProgress();

            const formData = new FormData();
            currentFolderFiles.forEach(file => {
                formData.append('files', file);
            });

            try {
                updateProgress(10, '{{ translations.batch_processing }}');

                const response = await fetch('/upload_folder', {
                    method: 'POST',
                    body: formData
                });

                updateProgress(90, '{{ translations.batch_processing }}');
                const result = await response.json();

                if (result.success) {
                    batchResults = result.batch_results;
                    currentBatchIndex = 0;
                    showBatchNavigation();
                    await loadCurrentBatchImage();
                    enableDownloadButtons();
                    showStatus(result.message, 'success');
                    updateProgress(100, '{{ translations.batch_completed }}');
                } else {
                    showStatus(result.error, 'error');
                    updateProgress(0);
                }
            } catch (error) {
                showStatus('{{ translations.processing_error }}: ' + error.message, 'error');
                updateProgress(0);
            } finally {
                isProcessing = false;
                setTimeout(hideProgress, 2000);
            }
        }

        function enableDownloadButtons() {
            document.getElementById('download-current').disabled = false;
            document.getElementById('download-all').disabled = false;
            document.getElementById('apply-to-all').disabled = false;
        }

        // Batch navigation
        function showBatchNavigation() {
            document.getElementById('batch-navigation').classList.add('visible');
            updateBatchNavigation();
        }

        function hideBatchNavigation() {
            document.getElementById('batch-navigation').classList.remove('visible');
        }

        function updateBatchNavigation() {
            if (!batchResults.length) return;

            const total = batchResults.length;
            const current = currentBatchIndex + 1;
            const filename = batchResults[currentBatchIndex].original_filename;

            document.getElementById('nav-counter').textContent = `${current} / ${total} - ${filename}`;
            document.getElementById('prev-btn').disabled = currentBatchIndex === 0;
            document.getElementById('next-btn').disabled = currentBatchIndex === total - 1;
        }

        async function loadCurrentBatchImage() {
            try {
                const response = await fetch('/get_current_image');
                const result = await response.json();

                if (result.success) {
                    showOriginalImage(result.original_image);
                    showResultImage(result.result_image);

                    // Update mirror checkbox
                    document.getElementById('mirror-horizontal').checked = result.mirror_horizontal;

                    // Update navigation
                    currentBatchIndex = result.current_index;
                    updateBatchNavigation();
                } else {
                    showStatus(result.error, 'error');
                }
            } catch (error) {
                showStatus('Error loading image: ' + error.message, 'error');
            }
        }

        async function previousImage() {
            try {
                const response = await fetch('/navigate_batch/prev');
                const result = await response.json();

                if (result.success) {
                    showOriginalImage(result.original_image);
                    showResultImage(result.result_image);
                    document.getElementById('mirror-horizontal').checked = result.mirror_horizontal;
                    currentBatchIndex = result.current_index;
                    updateBatchNavigation();
                }
            } catch (error) {
                showStatus('Navigation error: ' + error.message, 'error');
            }
        }

        async function nextImage() {
            try {
                const response = await fetch('/navigate_batch/next');
                const result = await response.json();

                if (result.success) {
                    showOriginalImage(result.original_image);
                    showResultImage(result.result_image);
                    document.getElementById('mirror-horizontal').checked = result.mirror_horizontal;
                    currentBatchIndex = result.current_index;
                    updateBatchNavigation();
                }
            } catch (error) {
                showStatus('Navigation error: ' + error.message, 'error');
            }
        }

        // Mirror functionality
        async function applyMirror() {
            const mirrorChecked = document.getElementById('mirror-horizontal').checked;

            try {
                const response = await fetch('/apply_mirror_batch', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        mirror_horizontal: mirrorChecked
                    })
                });

                const result = await response.json();

                if (result.success) {
                    showResultImage(result.result_image);
                } else {
                    showStatus(result.error, 'error');
                }
            } catch (error) {
                showStatus('{{ translations.processing_error }}: ' + error.message, 'error');
            }
        }

        // Apply to all remaining images
        async function applyToAll() {
            const mirrorChecked = document.getElementById('mirror-horizontal').checked;

            try {
                const response = await fetch('/apply_mirror_to_all', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        mirror_horizontal: mirrorChecked
                    })
                });

                const result = await response.json();

                if (result.success) {
                    showStatus(result.message, 'success');
                } else {
                    showStatus(result.error, 'error');
                }
            } catch (error) {
                showStatus('{{ translations.processing_error }}: ' + error.message, 'error');
            }
        }

        // Download functions
        function downloadCurrent() {
            window.open('/download_current', '_blank');
        }

        function downloadAll() {
            if (!batchResults.length) {
                showStatus('{{ translations.no_processed_image }}', 'error');
                return;
            }

            // Open download in new window
            window.open('/download_batch_zip', '_blank');
            showStatus('{{ translations.batch_export_success }}', 'success');
        }

        // Clear results
        async function clearResults() {
            try {
                // Clear server-side session
                await fetch('/clear_session');
            } catch (error) {
                console.log('Error clearing session:', error);
            }

            // Clear client-side data
            currentFolderFiles = [];
            batchResults = [];
            currentBatchIndex = 0;

            clearImages();
            hideBatchNavigation();
            hideProgress();

            document.getElementById('process-folder').disabled = true;
            document.getElementById('download-current').disabled = true;
            document.getElementById('download-all').disabled = true;
            document.getElementById('apply-to-all').disabled = true;

            document.getElementById('folder-files').value = '';
            document.getElementById('multiple-files').value = '';
            document.getElementById('mirror-horizontal').checked = true;

            document.getElementById('status-messages').innerHTML = '';

            showStatus('{{ translations.interface_cleared }}', 'info');
        }

        // Drag and drop functionality
        function setupDragAndDrop() {
            const uploadAreas = document.querySelectorAll('.file-upload');

            uploadAreas.forEach(area => {
                area.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    area.classList.add('dragover');
                });

                area.addEventListener('dragleave', (e) => {
                    e.preventDefault();
                    area.classList.remove('dragover');
                });

                area.addEventListener('drop', (e) => {
                    e.preventDefault();
                    area.classList.remove('dragover');

                    const files = e.dataTransfer.files;
                    if (files.length > 0) {
                        if (area.querySelector('#multiple-files')) {
                            // Multiple file upload
                            const input = document.getElementById('multiple-files');
                            input.files = files;
                            uploadMultiple(input);
                        }
                    }
                });
            });
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            setupDragAndDrop();
        });
    </script>
</body>
</html>
