# Medical Dental Analysis System - Web Application

This is the Flask web version of the Medical Dental Analysis System, providing the same functionality as the desktop PyQt5 application through a web interface.

## Features

### Core Functionality (Same as Desktop App)
- **YOLO-based mouth/teeth detection** using trained models
- **Automatic image orientation correction** (rotates vertical images)
- **Precise cropping** of detected mouth regions
- **Horizontal mirroring** controls for result images
- **Bilingual interface** (English/Italian)
- **Professional medical UI theme** matching the desktop version

### Single Image Processing
- Upload and process individual images
- Real-time preview of original and processed images
- Apply horizontal mirroring to results
- Download processed images

### Batch Processing
- Upload multiple images simultaneously
- Process entire folders of images
- Navigate through batch results with previous/next controls
- Apply mirror settings to individual images
- Export all results as a ZIP file

### Web-Specific Features
- **Responsive design** that works on desktop and mobile
- **Drag-and-drop file upload** interface
- **Real-time progress indicators** for processing
- **AJAX-based processing** without page reloads
- **Session management** for maintaining state

## Installation and Setup

### Prerequisites
- Python 3.8 or higher
- YOLO model files (see Model Requirements below)

### Install Dependencies
```bash
# Install web application requirements
pip install -r requirements_web.txt

# Or install full requirements (includes desktop dependencies)
pip install -r requirements.txt
```

### Model Requirements
The application requires YOLO model files in the project directory:

1. **Preferred**: `mouth_detector.pt` - Trained mouth detection model
2. **Fallback**: `yolov8n.pt` - General YOLO model (automatically downloaded)

If neither model is found, the application will show an error message.

### Running the Application

#### Development Mode
```bash
python web_app.py
```

The application will start on `http://localhost:5000`

#### Production Mode
For production deployment, use a WSGI server like Gunicorn:

```bash
# Install gunicorn
pip install gunicorn

# Run with gunicorn
gunicorn -w 4 -b 0.0.0.0:5000 web_app:app
```

## Usage

### Single Image Analysis
1. **Select Language**: Choose English or Italian from the dropdown
2. **Upload Image**: Click "Select Image" or drag-and-drop an image file
3. **Process**: Click "⚡ Analyze Image" to detect and crop mouth region
4. **Adjust**: Use the mirror checkbox to flip the result horizontally
5. **Download**: Click "💾 Download Result" to save the processed image

### Batch Processing
1. **Upload Multiple Images**: Click "Upload Images" and select multiple files
2. **Process Batch**: Click "⚡ Process Folder" to analyze all images
3. **Navigate Results**: Use Previous/Next buttons to review each result
4. **Adjust Individual Images**: Apply mirror settings to specific images
5. **Export All**: Click "📦 Export ZIP" to download all results

### Mirror Controls
- **Horizontal Mirror**: Flips the result image left-to-right
- Applied in real-time when checkbox is toggled
- Settings are preserved for each image in batch mode

## File Structure

```
├── web_app.py              # Main Flask application
├── templates/
│   └── index.html          # Main web interface template
├── uploads/                # Temporary uploaded files (auto-created)
├── results/                # Processed results (auto-created)
├── requirements_web.txt    # Web app dependencies
├── requirements.txt        # Full dependencies (desktop + web)
├── mouth_detector.pt       # Trained YOLO model (if available)
└── README_WEB.md          # This file
```

## API Endpoints

### Main Routes
- `GET /` - Main application interface
- `GET /set_language/<lang>` - Switch interface language

### Processing Routes
- `POST /upload_single` - Process single image
- `POST /upload_batch` - Process multiple images
- `POST /apply_mirror` - Apply mirror effects to current result

### Download Routes
- `GET /download_result` - Download single processed image
- `GET /download_batch_zip` - Download batch results as ZIP

### Utility Routes
- `GET /clear_session` - Clear session data and temporary files

## Configuration

### Application Settings
```python
# Maximum file upload size (default: 16MB)
MAX_CONTENT_LENGTH = 16 * 1024 * 1024

# Upload and results directories
UPLOAD_FOLDER = 'uploads'
RESULTS_FOLDER = 'results'

# Allowed file extensions
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'bmp', 'tiff', 'gif'}
```

### Security Considerations
- File uploads are validated for allowed extensions
- Filenames are sanitized using `secure_filename()`
- Session-based state management
- Temporary files are automatically cleaned up

## Differences from Desktop Version

### Advantages of Web Version
- **Cross-platform compatibility** - runs on any device with a web browser
- **No installation required** for end users
- **Centralized processing** - can be deployed on a server
- **Mobile-friendly** responsive design
- **Easy sharing** via URL

### Limitations Compared to Desktop
- **Network dependency** - requires internet connection to server
- **File size limits** - constrained by web upload limits
- **Session-based** - state is lost when browser is closed
- **No local file system access** - cannot directly browse local folders

## Deployment Options

### Local Development
- Run directly with `python web_app.py`
- Access at `http://localhost:5000`

### Network Deployment
- Deploy on local network server
- Access from multiple devices on same network
- Use production WSGI server (Gunicorn, uWSGI)

### Cloud Deployment
- Deploy to cloud platforms (Heroku, AWS, Google Cloud)
- Requires model files to be included in deployment
- Consider storage for uploaded/processed files

## Troubleshooting

### Common Issues
1. **Model not found**: Ensure `mouth_detector.pt` or `yolov8n.pt` is in project directory
2. **Upload fails**: Check file size limits and allowed extensions
3. **Processing errors**: Verify image files are valid and not corrupted
4. **Memory issues**: Large batch processing may require more RAM

### Performance Tips
- Use smaller image sizes for faster processing
- Process batches in smaller chunks for better memory usage
- Deploy on server with adequate RAM for YOLO model

## License and Credits

This web application is based on the original Medical Dental Analysis System desktop application, maintaining the same core functionality while providing web accessibility.
