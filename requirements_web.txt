# Medical Dental Analysis System - Web Application Requirements
# Install with: pip install -r requirements_web.txt

# Web Framework
Flask==2.3.3
Werkzeug==2.3.7

# Core Dependencies (from desktop app)
ultralytics>=8.0.0
opencv-python>=4.5.0
numpy>=1.21.0
Pillow>=8.0.0

# Optional: Production WSGI Server
# gunicorn>=20.1.0

# Optional: For containerized deployment
# docker

# Note: Ensure you have the YOLO model files:
# - mouth_detector.pt (preferred, trained model)
# - yolov8n.pt (fallback, general model)
