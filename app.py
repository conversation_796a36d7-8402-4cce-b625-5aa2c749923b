from flask import Flask, render_template_string, request, jsonify, send_file, session, redirect, url_for
import cv2
import numpy as np
import os
import zipfile
import base64
from datetime import datetime
import threading
import time
from werkzeug.utils import secure_filename
from ultralytics import YOLO
import uuid
import shutil
from io import BytesIO

app = Flask(__name__)
app.secret_key = 'medical_dental_analysis_secret_key_2024'

# Configuration
UPLOAD_FOLDER = 'uploads'
RESULTS_FOLDER = 'results'
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'bmp', 'tiff', 'gif'}
MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB max file size

app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['RESULTS_FOLDER'] = RESULTS_FOLDER
app.config['MAX_CONTENT_LENGTH'] = MAX_CONTENT_LENGTH

# Create necessary directories
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs(RESULTS_FOLDER, exist_ok=True)

# Try to load YOLO model
try:
    model = YOLO("mouth_detector.pt")
    model_info = {"status": "loaded", "message": "Mouth detection model loaded successfully"}
except:
    try:
        model = YOLO("yolov8n.pt")
        model_info = {"status": "general", "message": "Using general YOLO model. For best results, use a trained mouth detection model."}
    except Exception as e:
        model = None
        model_info = {"status": "error", "message": f"Could not load YOLO model: {str(e)}"}

# Language translations
LANGUAGES = {
    "en": {
        "select_folder": "📁 Select Folder",
        "process_folder": "⚡ Process Folder", 
        "export_zip": "📦 Export ZIP",
        "clear": "🗑️ Clear Results",
        "mirror_horizontal": "🪞 Mirror Left-Right",
        "mirror_horizontal_tooltip": "Apply horizontal mirror to the result image",
        "previous": "Previous",
        "next": "Next",
        "apply_to_all": "Apply to All Remaining",
        "original": "Original Image",
        "cropped_result": "Cropped Result",
        "images_display": "Images Display",
        "mirror_controls": "Mirror Controls",
        "folder_processing": "Folder Processing",
        "image_info": "Image Information",
        "analysis_log": "Analysis Log",
        "language_group": "Language / Lingua",
        "dental_analysis_header": "🦷 DENTAL ANALYSIS",
        "window_title": "Medical Dental Analysis System",
        "status": "Ready",
        "processing": "Processing...",
        "select_folder_first": "Select a folder to begin",
        "results_appear_here": "Results will appear here",
        "language": "Language",
        "ready_processing": "Ready for processing...",
        "image_loaded": "Image loaded successfully",
        "analysis_completed": "Analysis completed successfully",
        "no_image_selected": "Please select images first.",
        "no_processed_image": "No processed images to export.",
        "export_success": "Images exported successfully",
        "export_failed": "Failed to export images",
        "could_not_load": "Could not load the selected image files.",
        "processing_error": "Processing Error",
        "processing_failed": "Processing failed",
        "success": "Success",
        "error": "Error",
        "warning": "Warning",
        "interface_cleared": "Interface cleared and reset",
        "status_label": "Status:",
        "starting_analysis": "Starting image analysis...",
        "result_dimensions": "Result dimensions:",
        "pixels": "pixels",
        "image_loaded_log": "Images loaded:",
        "result_exported_log": "Results exported:",
        "export_failed_log": "Export failed:",
        "error_loading_log": "Error loading images:",
        "error_displaying": "Error displaying result:",
        "file_label": "Files:",
        "dimensions_label": "Dimensions:",
        "size_label": "Size:",
        "could_not_load_image": "Could not load the selected images.",
        "no_mouth_detected": "No mouth/teeth detected in the images.",
        "detected_region_empty": "Detected region is empty.",
        "processing_error_msg": "Processing error:",
        "model_info_title": "Model Info",
        "model_info_msg": "Using general YOLO model. For best results, use a trained mouth detection model.",
        "could_not_load_model": "Could not load YOLO model:",
        "batch_processing": "Batch Processing...",
        "batch_completed": "Batch processing completed",
        "images_processed": "images processed",
        "no_images_found": "No valid images found in the uploaded files.",
        "batch_export_success": "Batch results exported successfully",
        "exporting_progress": "Exporting images to ZIP file...",
        "files_exported": "files exported successfully",
        "upload_images": "Upload Images",
        "drag_drop_text": "Drag & drop image files here or click to browse",
        "max_file_size": "Maximum file size: 16MB per file",
        "supported_formats": "Supported formats: PNG, JPG, JPEG, BMP, TIFF, GIF"
    },
    "it": {
        "select_folder": "📁 Seleziona Cartella",
        "process_folder": "⚡ Elabora Cartella",
        "export_zip": "📦 Esporta ZIP", 
        "clear": "🗑️ Cancella Risultati",
        "mirror_horizontal": "🪞 Specchia Sinistra-Destra",
        "mirror_horizontal_tooltip": "Applica specchiatura orizzontale all'immagine risultato",
        "previous": "Precedente",
        "next": "Successivo",
        "apply_to_all": "Applica a Tutti i Rimanenti",
        "original": "Immagine Originale",
        "cropped_result": "Risultato Ritagliato",
        "images_display": "Visualizzazione Immagini",
        "mirror_controls": "Controlli Specchio",
        "folder_processing": "Elaborazione Cartella",
        "image_info": "Informazioni Immagine",
        "analysis_log": "Log di Analisi",
        "language_group": "Lingua / Language",
        "dental_analysis_header": "🦷 ANALISI DENTALE",
        "window_title": "Sistema di Analisi Dentale Medica",
        "status": "Pronto",
        "processing": "Elaborazione...",
        "select_folder_first": "Seleziona una cartella per iniziare",
        "results_appear_here": "I risultati appariranno qui",
        "language": "Lingua",
        "ready_processing": "Pronto per l'elaborazione...",
        "image_loaded": "Immagini caricate con successo",
        "analysis_completed": "Analisi completata con successo",
        "no_image_selected": "Seleziona prima delle immagini.",
        "no_processed_image": "Nessuna immagine elaborata da esportare.",
        "export_success": "Immagini esportate con successo",
        "export_failed": "Impossibile esportare le immagini",
        "could_not_load": "Impossibile caricare i file immagine selezionati.",
        "processing_error": "Errore di Elaborazione",
        "processing_failed": "Elaborazione fallita",
        "success": "Successo",
        "error": "Errore",
        "warning": "Avviso",
        "interface_cleared": "Interfaccia pulita e reimpostata",
        "status_label": "Stato:",
        "starting_analysis": "Avvio analisi immagine...",
        "result_dimensions": "Dimensioni risultato:",
        "pixels": "pixel",
        "image_loaded_log": "Immagini caricate:",
        "result_exported_log": "Risultati esportati:",
        "export_failed_log": "Esportazione fallita:",
        "error_loading_log": "Errore caricamento immagini:",
        "error_displaying": "Errore visualizzazione risultato:",
        "file_label": "File:",
        "dimensions_label": "Dimensioni:",
        "size_label": "Dimensione:",
        "could_not_load_image": "Impossibile caricare le immagini selezionate.",
        "no_mouth_detected": "Nessuna bocca/denti rilevata nelle immagini.",
        "detected_region_empty": "La regione rilevata è vuota.",
        "processing_error_msg": "Errore di elaborazione:",
        "model_info_title": "Info Modello",
        "model_info_msg": "Utilizzo modello YOLO generale. Per risultati migliori, usa un modello di rilevamento bocca addestrato.",
        "could_not_load_model": "Impossibile caricare il modello YOLO:",
        "batch_processing": "Elaborazione Lotto...",
        "batch_completed": "Elaborazione lotto completata",
        "images_processed": "immagini elaborate",
        "no_images_found": "Nessuna immagine valida trovata nei file caricati.",
        "batch_export_success": "Risultati lotto esportati con successo",
        "exporting_progress": "Esportazione immagini in file ZIP...",
        "files_exported": "file esportati con successo",
        "upload_images": "Carica Immagini",
        "drag_drop_text": "Trascina qui i file immagine o clicca per sfogliare",
        "max_file_size": "Dimensione massima file: 16MB per file",
        "supported_formats": "Formati supportati: PNG, JPG, JPEG, BMP, TIFF, GIF"
    }
}

def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def process_single_image(image_path, model):
    """Process a single image using YOLO detection"""
    try:
        # Load the image
        img = cv2.imread(image_path)
        if img is None:
            return None

        # Check if image is vertical and rotate 90 degrees to right if needed
        h, w = img.shape[:2]
        if h > w:  # Image is taller than wide (vertical)
            img = cv2.rotate(img, cv2.ROTATE_90_CLOCKWISE)

        # Save image for YOLO processing (after rotation adjustment)
        temp_path = f"temp_batch_{os.getpid()}_{uuid.uuid4().hex}.jpg"
        cv2.imwrite(temp_path, img)

        # Run YOLO model
        results = model(temp_path)

        # Clean up temporary file
        try:
            os.remove(temp_path)
        except:
            pass

        # Check if any detections were found
        if len(results[0].boxes) == 0:
            return None

        # Process detection
        for box in results[0].boxes:
            x1, y1, x2, y2 = map(int, box.xyxy[0])

            # Ensure coordinates are within image bounds
            x1, y1 = max(0, x1), max(0, y1)
            x2, y2 = min(img.shape[1], x2), min(img.shape[0], y2)

            roi = img[y1:y2, x1:x2]

            if roi.size == 0:
                return None

            # Return cropped region without any orientation correction
            return roi

    except Exception as e:
        print(f"Error processing {image_path}: {str(e)}")
        return None

def encode_image_to_base64(image):
    """Convert OpenCV image to base64 string for web display"""
    _, buffer = cv2.imencode('.png', image)
    img_str = base64.b64encode(buffer).decode()
    return f"data:image/png;base64,{img_str}"

# HTML Template
HTML_TEMPLATE = '''
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ translations.window_title }}</title>
    <style>
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Segoe UI', 'Arial', sans-serif;
            font-size: 10pt;
            background-color: #fafcff;
            color: #34495e;
            overflow-x: hidden;
        }

        .main-container {
            display: flex;
            min-height: 100vh;
            gap: 15px;
            padding: 15px;
        }

        .left-panel {
            width: 350px;
            min-width: 300px;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .right-panel {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .group-box {
            background: white;
            border: 2px solid #2980b9;
            border-radius: 8px;
            padding: 20px 15px 15px 15px;
            position: relative;
        }

        .group-box .title {
            position: absolute;
            top: -10px;
            left: 15px;
            background: white;
            color: #2980b9;
            font-weight: bold;
            font-size: 11pt;
            padding: 5px 10px;
            border-radius: 4px;
        }

        .header {
            text-align: center;
            font-size: 16pt;
            font-weight: bold;
            color: #2980b9;
            padding: 15px;
            background: linear-gradient(135deg, #ecf0f1, #ffffff);
            border-radius: 10px;
            margin-bottom: 10px;
        }

        .btn {
            background-color: #2980b9;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 12px 20px;
            font-weight: bold;
            font-size: 10pt;
            min-height: 44px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 10px;
        }

        .btn:hover {
            background-color: #1f6c9b;
            transform: translateY(-2px);
        }

        .btn:disabled {
            background-color: #ecf0f5;
            color: gray;
            cursor: not-allowed;
            transform: none;
        }

        .btn:active {
            background-color: #155887;
        }

        .form-control {
            background-color: white;
            border: 2px solid #2980b9;
            border-radius: 6px;
            padding: 8px;
            font-size: 10pt;
            min-height: 36px;
            width: 100%;
        }

        .checkbox-container {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px;
            font-weight: bold;
            font-size: 10pt;
        }

        .checkbox-container input[type="checkbox"] {
            width: 18px;
            height: 18px;
            border: 2px solid #2980b9;
            border-radius: 4px;
            background-color: white;
        }

        .checkbox-container input[type="checkbox"]:checked {
            background-color: #2980b9;
        }

        .images-section {
            display: flex;
            flex-direction: column;
            gap: 15px;
            height: 70vh;
        }

        .images-display {
            display: flex;
            gap: 15px;
            height: 60%;
        }

        .image-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .image-title {
            text-align: center;
            font-weight: bold;
            font-size: 12pt;
            color: #2c3e50;
            padding: 5px;
        }

        .image-display {
            flex: 1;
            border: 3px solid #bdc3c7;
            border-radius: 10px;
            background-color: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 300px;
            position: relative;
            overflow: hidden;
        }

        .image-display img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
            border-radius: 5px;
        }

        .image-display .placeholder {
            color: #7f8c8d;
            font-size: 14pt;
            text-align: center;
            padding: 20px;
        }

        .batch-navigation {
            display: none;
            flex-direction: row;
            gap: 10px;
            align-items: center;
            justify-content: center;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .batch-navigation.visible {
            display: flex;
        }

        .batch-counter {
            font-weight: bold;
            font-size: 12pt;
            margin: 0 15px;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #ecf0f5;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
            display: none;
        }

        .progress-bar.visible {
            display: block;
        }

        .progress-bar .progress {
            height: 100%;
            background-color: #27ae60;
            border-radius: 10px;
            transition: width 0.3s ease;
            width: 0%;
        }

        .log-area {
            background-color: white;
            border: 2px solid #bdc3c7;
            border-radius: 8px;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 9pt;
            white-space: pre-wrap;
        }

        .info-area {
            background-color: white;
            border: 2px solid #bdc3c7;
            border-radius: 8px;
            padding: 10px;
            height: 120px;
            overflow-y: auto;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 9pt;
            white-space: pre-wrap;
        }

        .status-bar {
            padding: 10px;
            background-color: #2c3e50;
            color: white;
            border-radius: 5px;
            font-weight: bold;
        }

        .upload-area {
            border: 3px dashed #2980b9;
            border-radius: 10px;
            padding: 30px;
            text-align: center;
            background-color: #f8f9fa;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 15px;
        }

        .upload-area:hover {
            background-color: #e8f4f8;
            border-color: #1f6c9b;
        }

        .upload-area.dragover {
            background-color: #d5e8f3;
            border-color: #155887;
        }

        .upload-text {
            font-size: 14pt;
            font-weight: bold;
            color: #2980b9;
            margin-bottom: 10px;
        }

        .upload-info {
            font-size: 9pt;
            color: #7f8c8d;
            line-height: 1.4;
        }

        .file-input {
            display: none;
        }

        .uploaded-files {
            margin-top: 10px;
            padding: 10px;
            background: white;
            border-radius: 5px;
            border: 1px solid #ddd;
        }

        .file-item {
            display: flex;
            justify-content: space-between;
            padding: 5px;
            border-bottom: 1px solid #eee;
        }

        .mirror-controls {
            height: 20%;
        }

        .alert {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }

        .alert-info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #b6d4db;
        }

        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        @media (max-width: 1200px) {
            .main-container {
                flex-direction: column;
            }
            
            .left-panel {
                width: 100%;
                min-width: auto;
            }
            
            .images-display {
                flex-direction: column;
                height: auto;
            }
            
            .images-section {
                height: auto;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Left Panel -->
        <div class="left-panel">
            <!-- Header -->
            <div class="header">{{ translations.dental_analysis_header }}</div>
            
            <!-- Language Selection -->
            <div class="group-box">
                <div class="title">{{ translations.language_group }}</div>
                <select class="form-control" id="languageSelect" onchange="changeLanguage(this.value)">
                    <option value="en" {% if session.get('language', 'en') == 'en' %}selected{% endif %}>🇺🇸 English</option>
                    <option value="it" {% if session.get('language', 'en') == 'it' %}selected{% endif %}>🇮🇹 Italiano</option>
                </select>
            </div>
            
            <!-- Upload and Processing Controls -->
            <div class="group-box">
                <div class="title">🔧 {{ translations.folder_processing }}</div>
                
                <!-- Upload Area -->
                <div class="upload-area" id="uploadArea" onclick="document.getElementById('fileInput').click()">
                    <div class="upload-text">{{ translations.drag_drop_text }}</div>
                    <div class="upload-info">
                        {{ translations.max_file_size }}<br>
                        {{ translations.supported_formats }}
                    </div>
                </div>
                <input type="file" id="fileInput" class="file-input" multiple accept=".png,.jpg,.jpeg,.bmp,.tiff,.gif" onchange="handleFileSelect(this.files)">
                
                <!-- Uploaded Files Display -->
                <div id="uploadedFiles" class="uploaded-files" style="display: none;"></div>
                
                <!-- Progress Bar -->
                <div class="progress-bar" id="progressBar">
                    <div class="progress" id="progressBarFill"></div>
                </div>
                
                <!-- Control Buttons -->
                <button class="btn" id="processBtn" onclick="processImages()" disabled>{{ translations.process_folder }}</button>
                <button class="btn" id="exportBtn" onclick="exportResults()" disabled>{{ translations.export_zip }}</button>
                <button class="btn" onclick="clearResults()">{{ translations.clear }}</button>
            </div>
            
            <!-- Image Information -->
            <div class="group-box">
                <div class="title">📊 {{ translations.image_info }}</div>
                <div class="info-area" id="imageInfo">{{ translations.select_folder_first }}</div>
            </div>
            
            <!-- Analysis Log -->
            <div class="group-box">
                <div class="title">📋 {{ translations.analysis_log }}</div>
                <div class="log-area" id="analysisLog">{{ translations.ready_processing }}</div>
            </div>
            
            <!-- Status Bar -->
            <div class="status-bar" id="statusBar">{{ translations.status_label }} {{ translations.status }}</div>
        </div>
        
        <!-- Right Panel -->
        <div class="right-panel">
            <!-- Images Display -->
            <div class="group-box images-section">
                <div class="title">🖼️ {{ translations.images_display }}</div>
                
                <div class="images-display">
                    <!-- Original Image -->
                    <div class="image-container">
                        <div class="image-title">📷 {{ translations.original }}</div>
                        <div class="image-display" id="originalImage">
                            <div class="placeholder">{{ translations.select_folder_first }}</div>
                        </div>
                    </div>
                    
                    <!-- Result Image -->
                    <div class="image-container">
                        <div class="image-title">🦷 {{ translations.cropped_result }}</div>
                        <div class="image-display" id="resultImage">
                            <div class="placeholder">{{ translations.results_appear_here }}</div>
                        </div>
                    </div>
                </div>
                
                <!-- Batch Navigation -->
                <div class="batch-navigation" id="batchNavigation">
                    <button class="btn" id="prevBtn" onclick="navigateBatch(-1)">◀ {{ translations.previous }}</button>
                    <div class="batch-counter" id="batchCounter">1 / 1</div>
                    <button class="btn" id="nextBtn" onclick="navigateBatch(1)">{{ translations.next }} ▶</button>
                    <button class="btn" id="applyAllBtn" onclick="applyToAll()">{{ translations.apply_to_all }}</button>
                </div>
            </div>
            
            <!-- Mirror Controls -->
            <div class="group-box mirror-controls">
                <div class="title">🪞 {{ translations.mirror_controls }}</div>
                <div class="checkbox-container">
                    <input type="checkbox" id="mirrorHorizontal" checked onchange="applyMirror()" disabled>
                    <label for="mirrorHorizontal">{{ translations.mirror_horizontal }}</label>
                </div>
            </div>
        </div>
    </div>

    <!-- Model Info Alert -->
    {% if model_info.status == 'general' %}
    <div class="alert alert-info" style="position: fixed; top: 20px; right: 20px; max-width: 400px; z-index: 1000;">
        {{ translations.model_info_title }}: {{ translations.model_info_msg }}
    </div>
    {% elif model_info.status == 'error' %}
    <div class="alert alert-error" style="position: fixed; top: 20px; right: 20px; max-width: 400px; z-index: 1000;">
        {{ translations.error }}: {{ model_info.message }}
    </div>
    {% endif %}

    <script>
        let uploadedFiles = [];
        let batchResults = [];
        let currentBatchIndex = 0;
        let processingActive = false;

        // Drag and drop functionality
        const uploadArea = document.getElementById('uploadArea');
        
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            handleFileSelect(e.dataTransfer.files);
        });

        function handleFileSelect(files) {
            uploadedFiles = Array.from(files).filter(file => {
                const ext = file.name.split('.').pop().toLowerCase();
                return ['png', 'jpg', 'jpeg', 'bmp', 'tiff', 'gif'].includes(ext);
            });

            displayUploadedFiles();
            updateProcessButton();
            logMessage(`📁 ${uploadedFiles.length} images selected`);
            updateStatus(`${uploadedFiles.length} images selected`);
        }

        function displayUploadedFiles() {
            const container = document.getElementById('uploadedFiles');
            if (uploadedFiles.length === 0) {
                container.style.display = 'none';
                return;
            }

            container.style.display = 'block';
            container.innerHTML = '<strong>Selected Files:</strong><br>';
            uploadedFiles.forEach((file, index) => {
                const fileItem = document.createElement('div');
                fileItem.className = 'file-item';
                fileItem.innerHTML = `
                    <span>${file.name}</span>
                    <span>${(file.size / (1024*1024)).toFixed(2)} MB</span>
                `;
                container.appendChild(fileItem);
            });

            updateImageInfo();
        }

        function updateImageInfo() {
            const info = document.getElementById('imageInfo');
            if (uploadedFiles.length === 0) {
                info.textContent = '{{ translations.select_folder_first }}';
                return;
            }

            let totalSize = uploadedFiles.reduce((sum, file) => sum + file.size, 0);
            let totalSizeMB = (totalSize / (1024 * 1024)).toFixed(2);

            info.innerHTML = `
📁 {{ translations.file_label }} ${uploadedFiles.length} files
💾 {{ translations.size_label }} ${totalSizeMB} MB total
🎨 {{ translations.supported_formats }}
            `.trim();
        }

        function updateProcessButton() {
            const processBtn = document.getElementById('processBtn');
            processBtn.disabled = uploadedFiles.length === 0 || processingActive;
        }

        function updateStatus(message) {
            document.getElementById('statusBar').innerHTML = `{{ translations.status_label }} ${message}`;
        }

        function logMessage(message) {
            const log = document.getElementById('analysisLog');
            const timestamp = new Date().toLocaleTimeString();
            log.textContent += `[${timestamp}] ${message}\n`;
            log.scrollTop = log.scrollHeight;
        }

        function showProgress(show, progress = 0) {
            const progressBar = document.getElementById('progressBar');
            const progressFill = document.getElementById('progressBarFill');
            
            if (show) {
                progressBar.classList.add('visible');
                progressFill.style.width = `${progress}%`;
            } else {
                progressBar.classList.remove('visible');
                progressFill.style.width = '0%';
            }
        }

        async function processImages() {
            if (uploadedFiles.length === 0) return;

            processingActive = true;
            updateProcessButton();
            showProgress(true, 0);
            updateStatus('{{ translations.processing }}');
            logMessage('{{ translations.starting_analysis }}');

            const formData = new FormData();
            uploadedFiles.forEach(file => {
                formData.append('files', file);
            });

            try {
                const response = await fetch('/process', {
                    method: 'POST',
                    body: formData
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();
                
                if (result.success) {
                    batchResults = result.results;
                    currentBatchIndex = 0;
                    
                    if (batchResults.length > 0) {
                        showBatchNavigation();
                        loadCurrentBatchResult();
                        document.getElementById('exportBtn').disabled = false;
                        updateStatus(`{{ translations.batch_completed }}: ${batchResults.length} {{ translations.images_processed }}`);
                        logMessage(`✅ {{ translations.batch_completed }}: ${batchResults.length} {{ translations.images_processed }}`);
                    } else {
                        updateStatus('{{ translations.no_mouth_detected }}');
                        logMessage('❌ {{ translations.no_mouth_detected }}');
                    }
                } else {
                    throw new Error(result.error || 'Processing failed');
                }
            } catch (error) {
                console.error('Error:', error);
                updateStatus('{{ translations.processing_failed }}');
                logMessage(`❌ {{ translations.error }}: ${error.message}`);
            } finally {
                processingActive = false;
                updateProcessButton();
                showProgress(false);
            }
        }

        function showBatchNavigation() {
            const nav = document.getElementById('batchNavigation');
            nav.classList.add('visible');
            updateBatchNavigation();
        }

        function hideBatchNavigation() {
            const nav = document.getElementById('batchNavigation');
            nav.classList.remove('visible');
        }

        function updateBatchNavigation() {
            if (batchResults.length === 0) return;

            const total = batchResults.length;
            const current = currentBatchIndex + 1;
            const filename = batchResults[currentBatchIndex].filename;

            document.getElementById('batchCounter').textContent = `${current} / ${total} - ${filename}`;
            document.getElementById('prevBtn').disabled = currentBatchIndex === 0;
            document.getElementById('nextBtn').disabled = currentBatchIndex === total - 1;
            document.getElementById('applyAllBtn').disabled = currentBatchIndex === total - 1;
        }

        function navigateBatch(direction) {
            const newIndex = currentBatchIndex + direction;
            if (newIndex >= 0 && newIndex < batchResults.length) {
                currentBatchIndex = newIndex;
                loadCurrentBatchResult();
            }
        }

        function loadCurrentBatchResult() {
            if (batchResults.length === 0) return;

            const result = batchResults[currentBatchIndex];
            
            // Display original image
            const originalDiv = document.getElementById('originalImage');
            originalDiv.innerHTML = `<img src="${result.original_image}" alt="Original Image">`;
            
            // Display processed image with current mirror settings
            displayProcessedImage();
            
            // Update mirror checkbox
            const mirrorCheckbox = document.getElementById('mirrorHorizontal');
            mirrorCheckbox.checked = result.mirror_horizontal !== false;
            mirrorCheckbox.disabled = false;
            
            // Update navigation
            updateBatchNavigation();
        }

        function displayProcessedImage() {
            if (batchResults.length === 0) return;

            const result = batchResults[currentBatchIndex];
            const resultDiv = document.getElementById('resultImage');
            
            // Use mirrored image if mirror is applied, otherwise use original processed image
            const imageToShow = result.mirror_horizontal !== false ? result.mirrored_image : result.processed_image;
            resultDiv.innerHTML = `<img src="${imageToShow}" alt="Processed Image">`;
        }

        function applyMirror() {
            if (batchResults.length === 0) return;

            const mirrorCheckbox = document.getElementById('mirrorHorizontal');
            const result = batchResults[currentBatchIndex];
            
            result.mirror_horizontal = mirrorCheckbox.checked;
            displayProcessedImage();
            
            // Re-enable apply to all button if there are remaining images
            if (currentBatchIndex < batchResults.length - 1) {
                document.getElementById('applyAllBtn').disabled = false;
            }
        }

        function applyToAll() {
            if (batchResults.length === 0) return;

            const currentMirror = document.getElementById('mirrorHorizontal').checked;
            
            // Apply current mirror setting to all remaining images
            for (let i = currentBatchIndex + 1; i < batchResults.length; i++) {
                batchResults[i].mirror_horizontal = currentMirror;
            }

            const remaining = batchResults.length - currentBatchIndex - 1;
            logMessage(`✅ Applied mirror setting to ${remaining} remaining images`);
            document.getElementById('applyAllBtn').disabled = true;
        }

        async function exportResults() {
            if (batchResults.length === 0) return;

            updateStatus('{{ translations.exporting_progress }}');
            showProgress(true, 0);

            try {
                const exportData = {
                    results: batchResults.map(result => ({
                        filename: result.filename,
                        mirror_horizontal: result.mirror_horizontal
                    }))
                };

                const response = await fetch('/export', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(exportData)
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                // Create download link
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5);
                a.href = url;
                a.download = `dental_analysis_batch_${timestamp}.zip`;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);

                updateStatus('{{ translations.batch_export_success }}');
                logMessage(`📦 {{ translations.batch_export_success }} (${batchResults.length} {{ translations.files_exported }})`);
            } catch (error) {
                console.error('Export error:', error);
                updateStatus('{{ translations.export_failed }}');
                logMessage(`❌ {{ translations.export_failed }}: ${error.message}`);
            } finally {
                showProgress(false);
            }
        }

        function clearResults() {
            // Clear uploaded files
            uploadedFiles = [];
            batchResults = [];
            currentBatchIndex = 0;
            
            // Reset UI
            document.getElementById('uploadedFiles').style.display = 'none';
            document.getElementById('originalImage').innerHTML = '<div class="placeholder">{{ translations.select_folder_first }}</div>';
            document.getElementById('resultImage').innerHTML = '<div class="placeholder">{{ translations.results_appear_here }}</div>';
            document.getElementById('analysisLog').textContent = '{{ translations.ready_processing }}';
            document.getElementById('imageInfo').textContent = '{{ translations.select_folder_first }}';
            
            // Reset controls
            document.getElementById('processBtn').disabled = true;
            document.getElementById('exportBtn').disabled = true;
            document.getElementById('mirrorHorizontal').disabled = true;
            document.getElementById('mirrorHorizontal').checked = true;
            
            hideBatchNavigation();
            showProgress(false);
            
            updateStatus('{{ translations.status }}');
            logMessage('🗑️ {{ translations.interface_cleared }}');
            
            // Reset file input
            document.getElementById('fileInput').value = '';
        }

        function changeLanguage(lang) {
            fetch('/change_language', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({language: lang})
            }).then(() => {
                window.location.reload();
            });
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus('{{ translations.status }}');
            
            // Show model info alert for a few seconds if it's a general model
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                setTimeout(() => {
                    alert.style.display = 'none';
                }, 5000);
            });
        });
    </script>
</body>
</html>
'''

@app.route('/')
def index():
    lang = session.get('language', 'en')
    translations = LANGUAGES[lang]
    return render_template_string(HTML_TEMPLATE, translations=translations, model_info=model_info)

@app.route('/change_language', methods=['POST'])
def change_language():
    data = request.get_json()
    lang = data.get('language', 'en')
    if lang in LANGUAGES:
        session['language'] = lang
    return jsonify({'success': True})

@app.route('/process', methods=['POST'])
def process_images():
    if model is None:
        return jsonify({'success': False, 'error': 'YOLO model not available'})
    
    uploaded_files = request.files.getlist('files')
    if not uploaded_files:
        return jsonify({'success': False, 'error': 'No files uploaded'})
    
    results = []
    session_id = str(uuid.uuid4())
    session_dir = os.path.join(app.config['UPLOAD_FOLDER'], session_id)
    os.makedirs(session_dir, exist_ok=True)
    
    try:
        for file in uploaded_files:
            if file and allowed_file(file.filename):
                # Save uploaded file
                filename = secure_filename(file.filename)
                filepath = os.path.join(session_dir, filename)
                file.save(filepath)
                
                # Process the image
                processed_image = process_single_image(filepath, model)
                
                if processed_image is not None:
                    # Create base64 encoded images
                    original_img = cv2.imread(filepath)
                    
                    # Check if original image is vertical and rotate for display
                    h, w = original_img.shape[:2]
                    if h > w:
                        original_img = cv2.rotate(original_img, cv2.ROTATE_90_CLOCKWISE)
                    
                    original_base64 = encode_image_to_base64(original_img)
                    processed_base64 = encode_image_to_base64(processed_image)
                    
                    # Create mirrored version
                    mirrored_image = cv2.flip(processed_image, 1)
                    mirrored_base64 = encode_image_to_base64(mirrored_image)
                    
                    result = {
                        'filename': filename,
                        'original_image': original_base64,
                        'processed_image': processed_base64,
                        'mirrored_image': mirrored_base64,
                        'mirror_horizontal': True,
                        'filepath': filepath
                    }
                    results.append(result)
        
        # Store session results
        session['session_id'] = session_id
        session['results'] = results
        
        return jsonify({
            'success': True,
            'results': results,
            'message': f'Processed {len(results)} images successfully'
        })
        
    except Exception as e:
        # Clean up on error
        if os.path.exists(session_dir):
            shutil.rmtree(session_dir)
        return jsonify({'success': False, 'error': str(e)})

@app.route('/export', methods=['POST'])
def export_results():
    try:
        data = request.get_json()
        export_results = data.get('results', [])
        session_results = session.get('results', [])
        session_id = session.get('session_id')
        
        if not session_results or not session_id:
            return jsonify({'error': 'No results to export'}), 400
        
        # Create temporary ZIP file
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        zip_filename = f"dental_analysis_batch_{timestamp}.zip"
        zip_path = os.path.join(app.config['RESULTS_FOLDER'], zip_filename)
        
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            used_filenames = set()
            
            for i, export_result in enumerate(export_results):
                # Find matching session result
                session_result = next((r for r in session_results if r['filename'] == export_result['filename']), None)
                if not session_result:
                    continue
                
                # Load the processed image from file
                filepath = session_result['filepath']
                if not os.path.exists(filepath):
                    continue
                
                processed_image = process_single_image(filepath, model)
                if processed_image is None:
                    continue
                
                # Apply mirror if requested
                if export_result.get('mirror_horizontal', True):
                    processed_image = cv2.flip(processed_image, 1)
                
                # Create unique filename
                original_name = os.path.splitext(export_result['filename'])[0]
                base_filename = f"{original_name}_processed"
                export_filename = f"{base_filename}.png"
                
                # Handle duplicate filenames
                counter = 1
                while export_filename in used_filenames:
                    export_filename = f"{base_filename}_{counter:03d}.png"
                    counter += 1
                
                used_filenames.add(export_filename)
                
                # Save image to temporary location
                temp_path = os.path.join(app.config['RESULTS_FOLDER'], f"temp_export_{i}_{timestamp}.png")
                cv2.imwrite(temp_path, processed_image)
                
                # Add to ZIP
                zipf.write(temp_path, export_filename)
                
                # Clean up temp file
                os.remove(temp_path)
        
        # Send the ZIP file
        return send_file(zip_path, as_attachment=True, download_name=zip_filename, mimetype='application/zip')
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    finally:
        # Clean up ZIP file after sending
        try:
            if 'zip_path' in locals() and os.path.exists(zip_path):
                # Schedule cleanup after a delay to ensure file is sent
                def cleanup():
                    time.sleep(2)
                    try:
                        os.remove(zip_path)
                    except:
                        pass
                threading.Thread(target=cleanup).start()
        except:
            pass

# Cleanup function to remove old session files
def cleanup_old_sessions():
    """Clean up session files older than 1 hour"""
    try:
        current_time = time.time()
        for session_dir in os.listdir(app.config['UPLOAD_FOLDER']):
            session_path = os.path.join(app.config['UPLOAD_FOLDER'], session_dir)
            if os.path.isdir(session_path):
                # Check if directory is older than 1 hour
                dir_time = os.path.getctime(session_path)
                if current_time - dir_time > 3600:  # 1 hour in seconds
                    shutil.rmtree(session_path)
    except:
        pass

# Schedule cleanup every hour
def schedule_cleanup():
    while True:
        time.sleep(3600)  # Wait 1 hour
        cleanup_old_sessions()

# Start cleanup thread
cleanup_thread = threading.Thread(target=schedule_cleanup, daemon=True)
cleanup_thread.start()

if __name__ == '__main__':
    print("=" * 60)
    print("🦷 MEDICAL DENTAL ANALYSIS SYSTEM - WEB VERSION")
    print("=" * 60)
    print(f"Model Status: {model_info['status']}")
    print(f"Model Info: {model_info['message']}")
    print("=" * 60)
    print("🌐 Web server starting...")
    print("📱 Access the application at: http://localhost:5000")
    print("🔧 Upload images and process them for dental analysis")
    print("📦 Export results as ZIP files")
    print("🌍 Multiple language support (English/Italian)")
    print("=" * 60)
    
    app.run(debug=True, host='0.0.0.0', port=5000)