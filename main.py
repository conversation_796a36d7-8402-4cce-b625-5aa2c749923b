import sys
import cv2
import numpy as np
from PyQt5.QtWidgets import (
    QApplication, QLabel, QPushButton, QVBoxLayout, QWidget, QHBoxLayout,
    QFileDialog, QComboBox, QMessageBox, QFrame, QGridLayout, QScrollArea,
    QSizePolicy, QSpacerItem, QProgressBar, QTextEdit, QGroupBox, QSplitter,
    QCheckBox, QListWidget, QListWidgetItem, QStackedWidget, QProgressDialog
)
from PyQt5.QtGui import QPixmap, QImage, QFont, QIcon, QPalette, QColor, QPainter, QBrush
from PyQt5.QtCore import Qt, QTimer, QThread, pyqtSignal
from ultralytics import YOLO
import os
import zipfile
from datetime import datetime

class BatchProcessingThread(QThread):
    """Separate thread for batch processing multiple images"""
    progress = pyqtSignal(int, str)  # progress percentage, current file
    finished = pyqtSignal(list)  # list of processed results
    error = pyqtSignal(str)  # error message

    def __init__(self, image_paths, model):
        super().__init__()
        self.image_paths = image_paths
        self.model = model
        self.results = []

    def run(self):
        try:
            total_images = len(self.image_paths)

            for i, image_path in enumerate(self.image_paths):
                filename = os.path.basename(image_path)
                self.progress.emit(int((i / total_images) * 100), f"Processing {filename}...")

                # Process single image
                result = self.process_single_image(image_path)
                if result is not None:
                    self.results.append({
                        'original_path': image_path,
                        'filename': filename,
                        'processed_image': result,
                        'mirror_horizontal': True  # Default mirror state
                    })

            self.progress.emit(100, "Batch processing completed!")
            self.finished.emit(self.results)

        except Exception as e:
            self.error.emit(f"Batch processing error: {str(e)}")

    def process_single_image(self, image_path):
        """Process a single image using the same logic as the original ProcessingThread"""
        try:
            # Load the image
            img = cv2.imread(image_path)
            if img is None:
                return None

            # Check if image is vertical and rotate 90 degrees to right if needed
            h, w = img.shape[:2]
            if h > w:  # Image is taller than wide (vertical)
                img = cv2.rotate(img, cv2.ROTATE_90_CLOCKWISE)
                print(f"Rotated vertical image 90° clockwise: {os.path.basename(image_path)}")

            # Save image for YOLO processing (after rotation adjustment)
            temp_path = f"temp_batch_{os.getpid()}_{os.path.basename(image_path)}"
            cv2.imwrite(temp_path, img)

            # STEP 1: Run YOLO model
            results = self.model(temp_path)

            # Clean up temporary file
            try:
                os.remove(temp_path)
            except:
                pass

            # Check if any detections were found
            if len(results[0].boxes) == 0:
                return None

            # Process detection
            for box in results[0].boxes:
                x1, y1, x2, y2 = map(int, box.xyxy[0])

                # Ensure coordinates are within image bounds
                x1, y1 = max(0, x1), max(0, y1)
                x2, y2 = min(img.shape[1], x2), min(img.shape[0], y2)

                roi = img[y1:y2, x1:x2]

                if roi.size == 0:
                    return None

                # Return cropped region without any orientation correction
                return roi

        except Exception as e:
            print(f"Error processing {image_path}: {str(e)}")
            return None





class ProcessingThread(QThread):
    """Separate thread for image processing to keep UI responsive"""
    finished = pyqtSignal(object)  # Emits processed image
    error = pyqtSignal(str)  # Emits error message
    progress = pyqtSignal(int)  # Emits progress percentage

    def __init__(self, image_path, model, translations):
        super().__init__()
        self.image_path = image_path
        self.model = model
        self.tr = translations
        
    def run(self):
        try:
            self.progress.emit(10)
            
            # Load the image
            img = cv2.imread(self.image_path)
            if img is None:
                self.error.emit(self.tr["could_not_load_image"])
                return

            self.progress.emit(30)

            # Check if image is vertical and rotate 90 degrees to right if needed
            h, w = img.shape[:2]
            if h > w:  # Image is taller than wide (vertical)
                img = cv2.rotate(img, cv2.ROTATE_90_CLOCKWISE)
                print(f"Rotated vertical image 90° clockwise")

            # Save image for YOLO processing (after rotation adjustment)
            temp_path = "temp_oriented.jpg"
            cv2.imwrite(temp_path, img)
            self.progress.emit(50)

            self.progress.emit(70)

            # STEP 1: Run YOLO model
            results = self.model(temp_path)

            # Clean up temporary file
            import os
            try:
                os.remove(temp_path)
            except:
                pass

            self.progress.emit(85)
            
            # Check if any detections were found
            if len(results[0].boxes) == 0:
                self.error.emit(self.tr["no_mouth_detected"])
                return

            # Process detection
            for box in results[0].boxes:
                x1, y1, x2, y2 = map(int, box.xyxy[0])
                
                # Ensure coordinates are within image bounds
                x1, y1 = max(0, x1), max(0, y1)
                x2, y2 = min(img.shape[1], x2), min(img.shape[0], y2)

                roi = img[y1:y2, x1:x2]
                
                if roi.size == 0:
                    self.error.emit(self.tr["detected_region_empty"])
                    return

                # Return cropped region without any orientation correction
                self.progress.emit(100)

                self.finished.emit(roi)
                break
                
        except Exception as e:
            self.error.emit(f"{self.tr['processing_error_msg']} {str(e)}")





class MedicalTeethApp(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Medical Dental Analysis System v2.0")
        self.image_path = None
        self.processing_thread = None
        
        # Try to load the model
        try:
            self.model = YOLO("mouth_detector.pt")
        except:
            try:
                self.model = YOLO("yolov8n.pt")
                # Model info message will be shown after UI is set up
                self.show_model_info = True
            except Exception as e:
                # Error message will be shown after UI is set up
                self.model_error = str(e)
                self.show_model_info = False

        # UI translations dictionary
        self.languages = {
            "en": {
                "select_image": "📁 Select Image",
                "process": "⚡ Analyze Image",
                "original": "Original Image",
                "result": "Processed Result",
                "cropped_result": "Cropped Result",
                "images_display": "Images Display",
                "mirror_controls": "Mirror Controls",
                "previous": "Previous",
                "next": "Next",
                "apply_to_all": "Apply to All Remaining",
                "process_folder": "⚡ Process Folder",
                "folder_processing": "Folder Processing",
                "select_folder_first": "Select a folder to begin",
                "results_appear_here": "Results will appear here",
                "language": "Language",
                "window_title": "Medical Dental Analysis System",
                "status": "Ready",
                "processing": "Processing...",
                "analysis_log": "Analysis Log",
                "image_info": "Image Information",
                "tools": "Analysis Tools",
                "export": "💾 Export Result",
                "clear": "🗑️ Clear Results",
                "zoom_in": "🔍 Zoom In",
                "zoom_out": "🔎 Zoom Out",
                "reset_zoom": "⚡ Reset View",
                "mirror_horizontal": "🪞 Mirror Left-Right",
                "mirror_horizontal_tooltip": "Apply horizontal mirror to the result image",
                "ready_processing": "Ready for processing...",
                "image_loaded": "Image loaded successfully",
                "analysis_completed": "Analysis completed successfully",
                "no_image_selected": "Please select an image first.",
                "no_processed_image": "No processed image to export.",
                "export_success": "Image exported successfully to:",
                "export_failed": "Failed to export image:",
                "could_not_load": "Could not load the selected image file.",
                "processing_error": "Processing Error",
                "processing_failed": "Processing failed",
                "success": "Success",
                "error": "Error",
                "warning": "Warning",
                "select_medical_image": "Select Medical Image",
                "export_processed_image": "Export Processed Image",
                "interface_cleared": "Interface cleared and reset",
                "dental_analysis_header": "🦷 DENTAL ANALYSIS",
                "language_group": "🌐 Language / Lingua",
                "status_label": "Status:",
                "starting_analysis": "Starting image analysis...",
                "result_dimensions": "Result dimensions:",
                "pixels": "pixels",
                "image_loaded_log": "Image loaded:",
                "result_exported_log": "Result exported:",
                "export_failed_log": "Export failed:",
                "error_loading_log": "Error loading image:",
                "error_displaying": "Error displaying result:",
                "file_label": "File:",
                "dimensions_label": "Dimensions:",
                "size_label": "Size:",
                "color_depth_label": "Color Depth:",
                "aspect_ratio_label": "Aspect Ratio:",
                "bits": "bits",
                "image_files_filter": "Image Files (*.png *.jpg *.jpeg *.bmp *.tiff *.gif *.dcm)",
                "png_files_filter": "PNG Files (*.png);;JPEG Files (*.jpg);;All Files (*)",
                "could_not_load_image": "Could not load the selected image.",
                "no_mouth_detected": "No mouth/teeth detected in the image.",
                "detected_region_empty": "Detected region is empty.",
                "processing_error_msg": "Processing error:",
                "model_info_title": "Model Info",
                "model_info_msg": "Using general YOLO model. For best results, use a trained mouth detection model.",
                "could_not_load_model": "Could not load YOLO model:",
                "select_folder": "📁 Select Folder",
                "batch_process": "⚡ Batch Process",
                "edit_results": "✏️ Edit Results",
                "export_zip": "📦 Export ZIP",
                "batch_processing": "Batch Processing...",
                "batch_completed": "Batch processing completed",
                "slideshow_mode": "Slideshow Mode",
                "previous_image": "◀ Previous",
                "next_image": "Next ▶",
                "apply_to_all": "Apply to All",
                "images_processed": "images processed",
                "no_images_found": "No valid images found in the selected folder.",
                "batch_export_success": "Batch results exported successfully to:",
                "exporting_progress": "Exporting images to ZIP file...",
                "files_exported": "files exported successfully",
                "slideshow_help": "Use arrow keys or buttons to navigate. Apply mirrors and click 'Apply to All' to apply to remaining images.",
            },
            "it": {
                "select_image": "📁 Seleziona Immagine",
                "process": "⚡ Analizza Immagine",
                "original": "Immagine Originale",
                "result": "Risultato Elaborato",
                "cropped_result": "Risultato Ritagliato",
                "images_display": "Visualizzazione Immagini",
                "mirror_controls": "Controlli Specchio",
                "previous": "Precedente",
                "next": "Successivo",
                "apply_to_all": "Applica a Tutti i Rimanenti",
                "process_folder": "⚡ Elabora Cartella",
                "folder_processing": "Elaborazione Cartella",
                "select_folder_first": "Seleziona una cartella per iniziare",
                "results_appear_here": "I risultati appariranno qui",
                "language": "Lingua",
                "window_title": "Sistema di Analisi Dentale Medica",
                "status": "Pronto",
                "processing": "Elaborazione...",
                "analysis_log": "Log di Analisi",
                "image_info": "Informazioni Immagine",
                "tools": "Strumenti di Analisi",
                "export": "💾 Esporta Risultato",
                "clear": "🗑️ Cancella Risultati",
                "zoom_in": "🔍 Ingrandisci",
                "zoom_out": "🔎 Riduci",
                "reset_zoom": "⚡ Reset Vista",
                "mirror_horizontal": "🪞 Specchia Sinistra-Destra",
                "mirror_horizontal_tooltip": "Applica specchiatura orizzontale all'immagine risultato",
                "ready_processing": "Pronto per l'elaborazione...",
                "image_loaded": "Immagine caricata con successo",
                "analysis_completed": "Analisi completata con successo",
                "no_image_selected": "Seleziona prima un'immagine.",
                "no_processed_image": "Nessuna immagine elaborata da esportare.",
                "export_success": "Immagine esportata con successo in:",
                "export_failed": "Impossibile esportare l'immagine:",
                "could_not_load": "Impossibile caricare il file immagine selezionato.",
                "processing_error": "Errore di Elaborazione",
                "processing_failed": "Elaborazione fallita",
                "success": "Successo",
                "error": "Errore",
                "warning": "Avviso",
                "select_medical_image": "Seleziona Immagine Medica",
                "export_processed_image": "Esporta Immagine Elaborata",
                "interface_cleared": "Interfaccia pulita e reimpostata",
                "dental_analysis_header": "🦷 ANALISI DENTALE",
                "language_group": "🌐 Lingua / Language",
                "status_label": "Stato:",
                "starting_analysis": "Avvio analisi immagine...",
                "result_dimensions": "Dimensioni risultato:",
                "pixels": "pixel",
                "image_loaded_log": "Immagine caricata:",
                "result_exported_log": "Risultato esportato:",
                "export_failed_log": "Esportazione fallita:",
                "error_loading_log": "Errore caricamento immagine:",
                "error_displaying": "Errore visualizzazione risultato:",
                "file_label": "File:",
                "dimensions_label": "Dimensioni:",
                "size_label": "Dimensione:",
                "color_depth_label": "Profondità Colore:",
                "aspect_ratio_label": "Rapporto Aspetto:",
                "bits": "bit",
                "image_files_filter": "File Immagine (*.png *.jpg *.jpeg *.bmp *.tiff *.gif *.dcm)",
                "png_files_filter": "File PNG (*.png);;File JPEG (*.jpg);;Tutti i File (*)",
                "could_not_load_image": "Impossibile caricare l'immagine selezionata.",
                "no_mouth_detected": "Nessuna bocca/denti rilevata nell'immagine.",
                "detected_region_empty": "La regione rilevata è vuota.",
                "processing_error_msg": "Errore di elaborazione:",
                "model_info_title": "Info Modello",
                "model_info_msg": "Utilizzo modello YOLO generale. Per risultati migliori, usa un modello di rilevamento bocca addestrato.",
                "could_not_load_model": "Impossibile caricare il modello YOLO:",
                "select_folder": "📁 Seleziona Cartella",
                "batch_process": "⚡ Elaborazione Lotto",
                "edit_results": "✏️ Modifica Risultati",
                "export_zip": "📦 Esporta ZIP",
                "batch_processing": "Elaborazione Lotto...",
                "batch_completed": "Elaborazione lotto completata",
                "slideshow_mode": "Modalità Presentazione",
                "previous_image": "◀ Precedente",
                "next_image": "Successivo ▶",
                "apply_to_all": "Applica a Tutti",
                "images_processed": "immagini elaborate",
                "no_images_found": "Nessuna immagine valida trovata nella cartella selezionata.",
                "batch_export_success": "Risultati lotto esportati con successo in:",
                "exporting_progress": "Esportazione immagini in file ZIP...",
                "files_exported": "file esportati con successo",
                "slideshow_help": "Usa i tasti freccia o i pulsanti per navigare. Applica specchiature e clicca 'Applica a Tutti' per applicare alle immagini rimanenti.",
            }
        }
        self.current_lang = "en"

        # Initialize result image storage
        self.original_result_image = None

        # Initialize batch processing variables
        self.batch_results = []
        self.current_batch_index = 0
        self.slideshow_mode = False
        self.slideshow_widget = None

        # Setup medical color scheme
        self.setup_medical_theme()
        self.setup_ui()
        self.setup_fullscreen()

    def setup_medical_theme(self):
        """Setup professional medical color scheme"""
        palette = QPalette()
        
        # Medical blue-white color scheme
        medical_blue = QColor(41, 128, 185)      # Professional blue
        light_blue = QColor(174, 214, 241)       # Light blue
        medical_white = QColor(250, 252, 255)    # Off-white
        medical_gray = QColor(236, 240, 245)     # Light gray
        dark_gray = QColor(52, 73, 94)           # Dark text
        success_green = QColor(39, 174, 96)      # Success color
        
        # Set palette colors
        palette.setColor(QPalette.Window, medical_white)
        palette.setColor(QPalette.WindowText, dark_gray)
        palette.setColor(QPalette.Base, QColor(255, 255, 255))
        palette.setColor(QPalette.AlternateBase, medical_gray)
        palette.setColor(QPalette.ToolTipBase, medical_blue)
        palette.setColor(QPalette.ToolTipText, QColor(255, 255, 255))
        palette.setColor(QPalette.Text, dark_gray)
        palette.setColor(QPalette.Button, medical_gray)
        palette.setColor(QPalette.ButtonText, dark_gray)
        palette.setColor(QPalette.BrightText, QColor(255, 0, 0))
        palette.setColor(QPalette.Link, medical_blue)
        palette.setColor(QPalette.Highlight, light_blue)
        palette.setColor(QPalette.HighlightedText, dark_gray)
        
        QApplication.setPalette(palette)
        
        # Define style sheet for modern medical look
        self.setStyleSheet(f"""
            QWidget {{
                font-family: 'Segoe UI', 'Arial', sans-serif;
                font-size: 10pt;
                background-color: {medical_white.name()};
                color: {dark_gray.name()};
            }}
            
            QGroupBox {{
                font-weight: bold;
                font-size: 11pt;
                border: 2px solid {medical_blue.name()};
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: white;
            }}
            
            QGroupBox::title {{
                subcontrol-origin: margin;
                subcontrol-position: top left;
                padding: 5px 10px;
                color: {medical_blue.name()};
                background-color: white;
                border-radius: 4px;
            }}
            
            QPushButton {{
                background-color: {medical_blue.name()};
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 10pt;
                min-height: 20px;
            }}
            
            QPushButton:hover {{
                background-color: {QColor(31, 108, 155).name()};
                transform: translateY(-2px);
            }}
            
            QPushButton:pressed {{
                background-color: {QColor(21, 88, 135).name()};
            }}
            
            QPushButton:disabled {{
                background-color: {medical_gray.name()};
                color: gray;
            }}
            
            QLabel {{
                background-color: white;
                border: 2px solid {medical_gray.name()};
                border-radius: 8px;
                padding: 10px;
            }}
            
            QComboBox {{
                background-color: white;
                border: 2px solid {medical_blue.name()};
                border-radius: 6px;
                padding: 8px;
                font-size: 10pt;
                min-height: 20px;
            }}
            
            QComboBox::drop-down {{
                border: none;
                width: 30px;
            }}
            
            QComboBox::down-arrow {{
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid {medical_blue.name()};
                margin-right: 10px;
            }}
            
            QProgressBar {{
                border: 2px solid {medical_blue.name()};
                border-radius: 6px;
                text-align: center;
                font-weight: bold;
                background-color: {medical_gray.name()};
            }}
            
            QProgressBar::chunk {{
                background-color: {success_green.name()};
                border-radius: 4px;
            }}
            
            QTextEdit {{
                border: 2px solid {medical_gray.name()};
                border-radius: 8px;
                padding: 10px;
                background-color: white;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 9pt;
            }}
            
            QScrollArea {{
                border: none;
                background-color: transparent;
            }}
            
            QSplitter::handle {{
                background-color: {medical_blue.name()};
                border-radius: 2px;
            }}
            
            QSplitter::handle:horizontal {{
                width: 6px;
            }}
            
            QSplitter::handle:vertical {{
                height: 6px;
            }}

            QCheckBox {{
                font-size: 10pt;
                font-weight: bold;
                color: {dark_gray.name()};
                spacing: 8px;
                padding: 8px;
            }}

            QCheckBox::indicator {{
                width: 18px;
                height: 18px;
                border: 2px solid {medical_blue.name()};
                border-radius: 4px;
                background-color: white;
            }}

            QCheckBox::indicator:checked {{
                background-color: {medical_blue.name()};
                image: none;
            }}

            QCheckBox::indicator:checked:after {{
                content: "✓";
                color: white;
                font-weight: bold;
                font-size: 12px;
            }}
        """)

    def setup_ui(self):
        """Setup the main UI with professional medical layout"""
        # Main layout with splitter for resizable panels
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(15)
        
        # Create main splitter
        main_splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(main_splitter)
        
        # Left panel for controls and info
        left_panel = self.create_left_panel()
        main_splitter.addWidget(left_panel)
        
        # Right panel for images
        right_panel = self.create_right_panel()
        main_splitter.addWidget(right_panel)
        
        # Set splitter proportions (30% left, 70% right)
        main_splitter.setSizes([300, 700])
        main_splitter.setStretchFactor(0, 0)
        main_splitter.setStretchFactor(1, 1)

    def create_left_panel(self):
        """Create the left control panel"""
        left_widget = QWidget()
        left_widget.setMaximumWidth(350)
        left_widget.setMinimumWidth(300)
        left_layout = QVBoxLayout(left_widget)
        left_layout.setSpacing(15)
        
        # Header with logo placeholder
        self.header = QLabel()
        self.header.setAlignment(Qt.AlignCenter)
        self.header.setStyleSheet("""
            font-size: 16pt;
            font-weight: bold;
            color: #2980b9;
            padding: 15px;
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #ecf0f1, stop:1 #ffffff);
            border-radius: 10px;
            margin-bottom: 10px;
        """)
        left_layout.addWidget(self.header)
        
        # Language selection group
        self.lang_group = QGroupBox()
        lang_layout = QVBoxLayout(self.lang_group)
        
        self.lang_box = QComboBox()
        self.lang_box.addItems(["🇺🇸 English", "🇮🇹 Italiano"])
        self.lang_box.currentIndexChanged.connect(self.switch_language)
        lang_layout.addWidget(self.lang_box)
        
        left_layout.addWidget(self.lang_group)
        
        # Image controls group
        controls_group = QGroupBox()
        controls_layout = QVBoxLayout(controls_group)
        controls_layout.setSpacing(10)
        
        # Batch processing buttons (folder-based processing only)
        self.btn_select_folder = QPushButton()
        self.btn_batch_process = QPushButton()
        self.btn_export_zip = QPushButton()
        self.btn_clear = QPushButton()

        # Batch processing connections
        self.btn_select_folder.clicked.connect(self.select_folder)
        self.btn_batch_process.clicked.connect(self.batch_process)
        self.btn_export_zip.clicked.connect(self.export_batch_zip)
        self.btn_clear.clicked.connect(self.clear_results)

        self.btn_batch_process.setEnabled(False)
        self.btn_export_zip.setEnabled(False)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        controls_layout.addWidget(self.progress_bar)

        # Batch processing controls (folder-based only)
        controls_layout.addWidget(self.btn_select_folder)
        controls_layout.addWidget(self.btn_batch_process)
        controls_layout.addWidget(self.btn_export_zip)
        controls_layout.addWidget(self.btn_clear)
        
        left_layout.addWidget(controls_group)
        
        # Image information group
        info_group = QGroupBox()
        info_layout = QVBoxLayout(info_group)
        
        self.image_info_text = QTextEdit()
        self.image_info_text.setMaximumHeight(120)
        self.image_info_text.setReadOnly(True)
        info_layout.addWidget(self.image_info_text)
        
        left_layout.addWidget(info_group)
        
        # Analysis log group
        log_group = QGroupBox()
        log_layout = QVBoxLayout(log_group)
        
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setMaximumHeight(200)
        log_layout.addWidget(self.log_text)
        
        left_layout.addWidget(log_group)
        
        # Status bar
        self.status_label = QLabel()
        self.status_label.setStyleSheet("""
            padding: 10px;
            background-color: #2c3e50;
            color: white;
            border-radius: 5px;
            font-weight: bold;
        """)
        left_layout.addWidget(self.status_label)
        
        # Stretch to push everything up
        left_layout.addStretch()
        
        return left_widget

    def create_right_panel(self):
        """Create the right image display panel"""
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        right_layout.setSpacing(15)
        
        # Image display area with splitter
        image_splitter = QSplitter(Qt.Vertical)
        right_layout.addWidget(image_splitter)
        
        # Original image group
        original_group = QGroupBox()
        original_layout = QVBoxLayout(original_group)
        
        # Original image display
        self.original_label = QLabel()
        self.original_label.setAlignment(Qt.AlignCenter)
        self.original_label.setMinimumHeight(300)
        self.original_label.setScaledContents(False)  # Preserve aspect ratio
        self.original_label.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.original_label.setStyleSheet("""
            QLabel {
                border: 3px solid #bdc3c7;
                border-radius: 10px;
                background-color: #f8f9fa;
            }
        """)


        # Create horizontal layout for side-by-side images
        images_horizontal_layout = QHBoxLayout()

        # Original image sub-section
        original_sub_layout = QVBoxLayout()
        self.original_title = QLabel("📷 Original")
        self.original_title.setAlignment(Qt.AlignCenter)
        self.original_title.setStyleSheet("font-weight: bold; font-size: 12pt; margin: 5px; color: #2c3e50;")
        original_sub_layout.addWidget(self.original_title)
        original_sub_layout.addWidget(self.original_label)

        # Result image sub-section
        result_sub_layout = QVBoxLayout()
        self.result_title = QLabel("🦷 Cropped Result")
        self.result_title.setAlignment(Qt.AlignCenter)
        self.result_title.setStyleSheet("font-weight: bold; font-size: 12pt; margin: 5px; color: #2c3e50;")

        # Result image display
        self.result_label = QLabel()
        self.result_label.setAlignment(Qt.AlignCenter)
        self.result_label.setMinimumHeight(300)
        self.result_label.setScaledContents(False)  # Preserve aspect ratio
        self.result_label.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.result_label.setStyleSheet("""
            QLabel {
                border: 3px solid #bdc3c7;
                border-radius: 10px;
                background-color: #f8f9fa;
            }
        """)

        result_sub_layout.addWidget(self.result_title)
        result_sub_layout.addWidget(self.result_label)

        # Add both sub-layouts to horizontal layout
        images_horizontal_layout.addLayout(original_sub_layout)
        images_horizontal_layout.addLayout(result_sub_layout)

        # Add batch navigation controls in a widget container
        self.batch_nav_widget = QWidget()
        self.batch_nav_layout = QHBoxLayout(self.batch_nav_widget)
        self.batch_nav_widget.setVisible(False)  # Hidden by default

        # Previous button
        self.btn_previous = QPushButton("◀ Previous")
        self.btn_previous.clicked.connect(self.previous_batch_image)
        self.btn_previous.setEnabled(False)
        self.batch_nav_layout.addWidget(self.btn_previous)

        # Image counter
        self.batch_counter_label = QLabel("1 / 1")
        self.batch_counter_label.setAlignment(Qt.AlignCenter)
        self.batch_counter_label.setStyleSheet("font-weight: bold; font-size: 12pt; margin: 10px;")
        self.batch_nav_layout.addWidget(self.batch_counter_label)

        # Next button
        self.btn_next = QPushButton("Next ▶")
        self.btn_next.clicked.connect(self.next_batch_image)
        self.btn_next.setEnabled(False)
        self.batch_nav_layout.addWidget(self.btn_next)

        # Apply to all button
        self.btn_apply_to_all = QPushButton("Apply to All Remaining")
        self.btn_apply_to_all.clicked.connect(self.apply_mirrors_to_all)
        self.btn_apply_to_all.setEnabled(False)
        self.batch_nav_layout.addWidget(self.btn_apply_to_all)

        # Add horizontal layout and navigation to the original group
        original_layout.addLayout(images_horizontal_layout)
        original_layout.addWidget(self.batch_nav_widget)

        image_splitter.addWidget(original_group)

        # Mirror options group (separate section)
        mirror_group = QGroupBox()
        mirror_layout = QVBoxLayout(mirror_group)

        # Mirror options layout (horizontal only)
        mirror_options_layout = QHBoxLayout()

        # Horizontal mirror checkbox
        self.mirror_horizontal_checkbox = QCheckBox()
        self.mirror_horizontal_checkbox.setChecked(True)  # Enabled by default
        self.mirror_horizontal_checkbox.stateChanged.connect(self.on_mirror_changed)
        self.mirror_horizontal_checkbox.setEnabled(False)  # Disabled until result is available
        mirror_options_layout.addWidget(self.mirror_horizontal_checkbox)

        mirror_layout.addLayout(mirror_options_layout)
        image_splitter.addWidget(mirror_group)

        # Set equal sizes for both image areas
        image_splitter.setSizes([600, 200])
        
        return right_widget

    def setup_fullscreen(self):
        """Setup fullscreen display"""
        self.showMaximized()
        self.setMinimumSize(1200, 800)
        # Apply initial translation
        self.update_texts()

        # Show model info or error after UI is ready
        if hasattr(self, 'show_model_info') and self.show_model_info:
            tr = self.languages[self.current_lang]
            QMessageBox.information(self, tr["model_info_title"], tr["model_info_msg"])
        elif hasattr(self, 'model_error'):
            tr = self.languages[self.current_lang]
            QMessageBox.critical(self, tr["error"], f"{tr['could_not_load_model']} {self.model_error}")
            sys.exit(1)

    def switch_language(self, index):
        """Switch UI language based on dropdown selection."""
        self.current_lang = "en" if index == 0 else "it"
        self.update_texts()

    def on_mirror_changed(self, state):
        """Handle mirror checkbox state change - apply horizontal mirror to displayed result"""
        if hasattr(self, 'original_result_image') and self.original_result_image is not None:
            # Start with original image
            result_image = self.original_result_image.copy()

            # Apply horizontal mirror if checked
            if self.mirror_horizontal_checkbox.isChecked():
                result_image = cv2.flip(result_image, 1)  # 1 = horizontal flip

            # Display the result with applied mirror
            self.display_result_image(result_image)

            # Update batch results if in batch mode
            if hasattr(self, 'batch_results') and hasattr(self, 'current_batch_index'):
                if self.batch_results and 0 <= self.current_batch_index < len(self.batch_results):
                    self.batch_results[self.current_batch_index]['mirror_horizontal'] = self.mirror_horizontal_checkbox.isChecked()

                    # Re-enable apply to all button if there are remaining images
                    if self.current_batch_index < len(self.batch_results) - 1:
                        self.btn_apply_to_all.setEnabled(True)

    def update_texts(self):
        """Update all UI elements based on selected language."""
        tr = self.languages[self.current_lang]
        self.setWindowTitle(tr["window_title"])

        # Main processing buttons (folder-based only)
        self.btn_select_folder.setText(tr["select_folder"])
        self.btn_batch_process.setText(tr["process_folder"])
        self.btn_export_zip.setText(tr["export_zip"])
        self.btn_clear.setText(tr["clear"])

        # Batch navigation buttons
        if hasattr(self, 'btn_previous'):
            self.btn_previous.setText("◀ " + tr["previous"])
        if hasattr(self, 'btn_next'):
            self.btn_next.setText(tr["next"] + " ▶")
        if hasattr(self, 'btn_apply_to_all'):
            self.btn_apply_to_all.setText(tr["apply_to_all"])

        self.mirror_horizontal_checkbox.setText(tr["mirror_horizontal"])
        self.mirror_horizontal_checkbox.setToolTip(tr["mirror_horizontal_tooltip"])

        # Update header and language group
        self.header.setText(tr["dental_analysis_header"])
        self.lang_group.setTitle(tr["language_group"])
        
        # Update group box titles by finding them through their widgets
        for group_box in self.findChildren(QGroupBox):
            if hasattr(self, 'image_info_text') and self.image_info_text.parent().parent() == group_box:
                group_box.setTitle("📊 " + tr["image_info"])
            elif hasattr(self, 'log_text') and self.log_text.parent().parent() == group_box:
                group_box.setTitle("📋 " + tr["analysis_log"])
            elif hasattr(self, 'btn_select_folder') and self.btn_select_folder.parent().parent() == group_box:
                group_box.setTitle("🔧 " + tr["folder_processing"])
            elif hasattr(self, 'original_label') and self.original_label.parent().parent().parent() == group_box:
                group_box.setTitle("�️ " + tr["images_display"])
            elif hasattr(self, 'mirror_horizontal_checkbox') and self.mirror_horizontal_checkbox.parent().parent().parent() == group_box:
                group_box.setTitle("🪞 " + tr["mirror_controls"])
        
        # Update individual image titles
        if hasattr(self, 'original_title'):
            self.original_title.setText("📷 " + tr["original"])
        if hasattr(self, 'result_title'):
            self.result_title.setText("🦷 " + tr["cropped_result"])

        # Update default text if no image is loaded
        if not hasattr(self, 'image_path') or not self.image_path:
            self.original_label.setText(tr["original"])
            self.result_label.setText(tr["result"])
            self.update_status(tr["status"])

    def update_status(self, message):
        """Update status bar message"""
        tr = self.languages[self.current_lang]
        self.status_label.setText(f"{tr['status_label']} {message}")

    def log_message(self, message):
        """Add message to analysis log"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] {message}")



    def update_image_info(self, file_path, pixmap):
        """Update image information display"""
        import os
        tr = self.languages[self.current_lang]
        file_size = os.path.getsize(file_path)
        file_size_mb = file_size / (1024 * 1024)

        info_text = f"""
📁 {tr['file_label']} {os.path.basename(file_path)}
📐 {tr['dimensions_label']} {pixmap.width()} × {pixmap.height()} {tr['pixels']}
💾 {tr['size_label']} {file_size_mb:.2f} MB
🎨 {tr['color_depth_label']} {pixmap.depth()} {tr['bits']}
📏 {tr['aspect_ratio_label']} {pixmap.width()/pixmap.height():.2f}
        """.strip()

        self.image_info_text.setPlainText(info_text)



    def on_progress_update(self, value):
        """Update progress bar"""
        self.progress_bar.setValue(value)



    def display_result_image(self, image):
        """Helper method to display result image in the UI with adaptive scaling"""
        try:
            # Convert OpenCV image to QPixmap
            h, w, ch = image.shape
            bytes_per_line = ch * w

            # Convert BGR to RGB
            rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

            # Create QImage
            q_img = QImage(rgb_image.data.tobytes(), w, h, bytes_per_line, QImage.Format_RGB888)
            pixmap = QPixmap.fromImage(q_img)

            # Get available display area
            available_size = self.result_label.size()

            # Calculate optimal scaling based on image aspect ratio
            image_aspect = w / h
            display_aspect = available_size.width() / available_size.height()

            # Scale to fit while preserving aspect ratio and image orientation
            if image_aspect > display_aspect:
                # Image is wider - fit to width
                target_width = min(available_size.width() - 20, w)  # Leave some margin
                target_height = int(target_width / image_aspect)
            else:
                # Image is taller - fit to height
                target_height = min(available_size.height() - 20, h)  # Leave some margin
                target_width = int(target_height * image_aspect)

            # Apply scaling with smooth transformation
            scaled_pixmap = pixmap.scaled(target_width, target_height, Qt.KeepAspectRatio, Qt.SmoothTransformation)
            self.result_label.setPixmap(scaled_pixmap)

        except Exception as e:
            print(f"Error displaying image: {str(e)}")

    def display_original_image_from_pixmap(self, pixmap):
        """Helper method to display original image from QPixmap"""
        try:
            # Get available display area
            available_size = self.original_label.size()
            image_size = pixmap.size()

            # Calculate optimal scaling based on image aspect ratio
            image_aspect = image_size.width() / image_size.height()
            display_aspect = available_size.width() / available_size.height()

            # Scale to fit while preserving aspect ratio and image orientation
            if image_aspect > display_aspect:
                # Image is wider - fit to width
                target_width = min(available_size.width() - 20, image_size.width())
                target_height = int(target_width / image_aspect)
            else:
                # Image is taller - fit to height
                target_height = min(available_size.height() - 20, image_size.height())
                target_width = int(target_height * image_aspect)

            scaled_pixmap = pixmap.scaled(target_width, target_height, Qt.KeepAspectRatio, Qt.SmoothTransformation)
            self.original_label.setPixmap(scaled_pixmap)

        except Exception as e:
            print(f"Error displaying original image: {str(e)}")




        self.progress_bar.setValue(0)
        
        # Clean up thread
        if self.processing_thread:
            self.processing_thread.quit()
            self.processing_thread.wait()
            self.processing_thread = None



    def clear_results(self):
        """Clear all results and reset the interface"""
        self.original_label.clear()
        self.result_label.clear()
        self.image_info_text.clear()
        self.log_text.clear()

        # Clear stored result image and disable mirror checkbox
        if hasattr(self, 'original_result_image'):
            self.original_result_image = None
        self.mirror_horizontal_checkbox.setEnabled(False)
        self.mirror_horizontal_checkbox.setChecked(True)  # Reset to default (enabled)

        # Hide batch navigation and clear batch results
        if hasattr(self, 'batch_nav_widget'):
            self.hide_batch_navigation()
        if hasattr(self, 'batch_results'):
            self.batch_results = None
        if hasattr(self, 'current_batch_index'):
            self.current_batch_index = 0
        if hasattr(self, 'image_paths'):
            self.image_paths = []

        tr = self.languages[self.current_lang]
        self.original_label.setText(tr["select_folder_first"])
        self.result_label.setText(tr["results_appear_here"])

        self.btn_batch_process.setEnabled(False)
        self.btn_export_zip.setEnabled(False)

        self.update_status(tr["status"])
        self.log_message("🗑️ " + tr["interface_cleared"])

    def select_folder(self):
        """Select folder for batch processing"""
        tr = self.languages[self.current_lang]
        folder_path = QFileDialog.getExistingDirectory(
            self,
            tr["select_folder"],
            ""
        )

        if folder_path:
            # Find all image files in the folder (case-insensitive, no duplicates)
            image_extensions = ['.png', '.jpg', '.jpeg', '.bmp', '.tiff', '.gif']
            image_paths = []

            # Get all files in the folder
            for filename in os.listdir(folder_path):
                file_path = os.path.join(folder_path, filename)
                if os.path.isfile(file_path):
                    # Check if file extension matches (case-insensitive)
                    file_ext = os.path.splitext(filename)[1].lower()
                    if file_ext in image_extensions:
                        image_paths.append(file_path)

            if image_paths:
                self.folder_path = folder_path
                self.image_paths = sorted(image_paths)
                self.btn_batch_process.setEnabled(True)
                self.update_status(f"{len(image_paths)} images found in folder")
                self.log_message(f"📁 Folder selected: {len(image_paths)} images found")

                # Debug: Log the first few filenames to verify
                print(f"Debug: Found {len(image_paths)} images:")
                for i, path in enumerate(image_paths[:5]):  # Show first 5
                    print(f"  {i+1}. {os.path.basename(path)}")
                if len(image_paths) > 5:
                    print(f"  ... and {len(image_paths) - 5} more")

            else:
                QMessageBox.warning(self, tr["warning"], tr["no_images_found"])

    def batch_process(self):
        """Start batch processing of all images in the selected folder"""
        if not hasattr(self, 'image_paths') or not self.image_paths:
            return

        tr = self.languages[self.current_lang]
        self.update_status(tr["batch_processing"])
        self.log_message(tr["starting_analysis"])

        # Disable buttons during processing
        self.btn_batch_process.setEnabled(False)
        self.btn_select_folder.setEnabled(False)
        self.progress_bar.setVisible(True)

        # Start batch processing thread
        self.batch_processing_thread = BatchProcessingThread(self.image_paths, self.model)
        self.batch_processing_thread.progress.connect(self.on_batch_progress)
        self.batch_processing_thread.finished.connect(self.on_batch_finished)
        self.batch_processing_thread.error.connect(self.on_batch_error)
        self.batch_processing_thread.start()

    def on_batch_progress(self, progress, message):
        """Handle batch processing progress updates"""
        self.progress_bar.setValue(progress)
        self.update_status(message)

    def on_batch_finished(self, results):
        """Handle batch processing completion"""
        tr = self.languages[self.current_lang]
        self.batch_results = results

        # Re-enable buttons
        self.btn_batch_process.setEnabled(True)
        self.btn_select_folder.setEnabled(True)
        self.progress_bar.setVisible(False)

        if results:
            self.current_batch_index = 0
            # Show batch navigation and load first result
            self.show_batch_navigation()
            self.load_current_batch_result()

            self.btn_export_zip.setEnabled(True)
            self.update_status(f"{tr['batch_completed']}: {len(results)} {tr['images_processed']}")
            self.log_message(f"✅ {tr['batch_completed']}: {len(results)} {tr['images_processed']}")
        else:
            self.update_status(tr["no_mouth_detected"])
            self.log_message("❌ " + tr["no_mouth_detected"])

    def on_batch_error(self, error_message):
        """Handle batch processing error"""
        tr = self.languages[self.current_lang]
        QMessageBox.critical(self, tr["processing_error"], error_message)
        self.update_status(tr["processing_failed"])
        self.log_message(f"❌ Error: {error_message}")

        # Re-enable buttons
        self.btn_batch_process.setEnabled(True)
        self.btn_select_folder.setEnabled(True)
        self.progress_bar.setVisible(False)

    def show_batch_navigation(self):
        """Show batch navigation controls"""
        self.batch_nav_widget.setVisible(True)
        self.update_batch_navigation()

    def hide_batch_navigation(self):
        """Hide batch navigation controls"""
        self.batch_nav_widget.setVisible(False)

    def update_batch_navigation(self):
        """Update batch navigation button states and counter"""
        if not hasattr(self, 'batch_results') or not self.batch_results:
            return

        total = len(self.batch_results)
        current = self.current_batch_index + 1

        # Update counter
        filename = self.batch_results[self.current_batch_index]['filename']
        self.batch_counter_label.setText(f"{current} / {total} - {filename}")

        # Update button states
        self.btn_previous.setEnabled(self.current_batch_index > 0)
        self.btn_next.setEnabled(self.current_batch_index < total - 1)
        self.btn_apply_to_all.setEnabled(self.current_batch_index < total - 1)

    def previous_batch_image(self):
        """Navigate to previous batch result"""
        if self.current_batch_index > 0:
            self.current_batch_index -= 1
            self.load_current_batch_result()

    def next_batch_image(self):
        """Navigate to next batch result"""
        if hasattr(self, 'batch_results') and self.current_batch_index < len(self.batch_results) - 1:
            self.current_batch_index += 1
            self.load_current_batch_result()

    def load_current_batch_result(self):
        """Load and display current batch result"""
        if not hasattr(self, 'batch_results') or not self.batch_results:
            return

        current_result = self.batch_results[self.current_batch_index]

        # Load original image
        original_path = current_result['original_path']
        original_pixmap = QPixmap(original_path)
        if not original_pixmap.isNull():
            self.display_original_image_from_pixmap(original_pixmap)

        # Load and display processed result
        processed_image = current_result['processed_image'].copy()

        # Apply current mirror settings (horizontal only)
        if current_result.get('mirror_horizontal', True):
            processed_image = cv2.flip(processed_image, 1)

        # Store original for mirror functionality
        self.original_result_image = current_result['processed_image'].copy()

        # Update mirror checkbox
        self.mirror_horizontal_checkbox.setChecked(current_result.get('mirror_horizontal', True))
        self.mirror_horizontal_checkbox.setEnabled(True)

        # Display result
        self.display_result_image(processed_image)

        # Update navigation
        self.update_batch_navigation()

    def apply_mirrors_to_all(self):
        """Apply current horizontal mirror setting to all remaining images"""
        if not hasattr(self, 'batch_results') or not self.batch_results:
            return

        current_h_mirror = self.mirror_horizontal_checkbox.isChecked()

        # Apply to all remaining images
        for i in range(self.current_batch_index + 1, len(self.batch_results)):
            self.batch_results[i]['mirror_horizontal'] = current_h_mirror

        remaining = len(self.batch_results) - self.current_batch_index - 1
        self.log_message(f"✅ Applied horizontal mirror setting to {remaining} remaining images")

        # Update button state
        self.btn_apply_to_all.setEnabled(False)

    def edit_batch_results(self):
        """Open slideshow mode for editing batch results"""
        if not self.batch_results:
            return

        self.slideshow_mode = True
        self.current_batch_index = 0
        self.create_slideshow_widget()

    def create_slideshow_widget(self):
        """Create the slideshow interface for editing batch results"""
        tr = self.languages[self.current_lang]

        # Create slideshow widget with same size as main window
        self.slideshow_widget = QWidget()
        self.slideshow_widget.setWindowTitle(tr["slideshow_mode"])

        # Set same size and position as main window
        main_geometry = self.geometry()
        self.slideshow_widget.setGeometry(main_geometry)
        self.slideshow_widget.setMinimumSize(1200, 800)  # Same as main window

        # Apply same medical theme styling as main window
        self.slideshow_widget.setStyleSheet("""
            QWidget {
                background-color: #fafcff;
                font-family: 'Segoe UI', 'Arial', sans-serif;
                font-size: 10pt;
                color: #34495e;
            }
            QPushButton {
                background-color: #2980b9;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 10pt;
                min-height: 20px;
            }
            QPushButton:hover {
                background-color: #1f6c9b;
            }
            QPushButton:disabled {
                background-color: #ecf0f5;
                color: gray;
            }
            QCheckBox {
                font-size: 10pt;
                font-weight: bold;
                color: #34495e;
                spacing: 8px;
                padding: 8px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border: 2px solid #2980b9;
                border-radius: 4px;
                background-color: white;
            }
            QCheckBox::indicator:checked {
                background-color: #2980b9;
            }
            QLabel {
                color: #34495e;
            }
        """)

        layout = QVBoxLayout(self.slideshow_widget)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(15)

        # Header section with title and info
        header_frame = QFrame()
        header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                            stop:0 #ecf0f1, stop:1 #ffffff);
                border-radius: 10px;
                padding: 15px;
                margin-bottom: 10px;
            }
        """)
        header_layout = QVBoxLayout(header_frame)

        # Title and navigation info
        title_layout = QHBoxLayout()
        slideshow_title = QLabel(tr["slideshow_mode"])
        slideshow_title.setStyleSheet("""
            font-size: 16pt;
            font-weight: bold;
            color: #2980b9;
        """)

        self.slideshow_info = QLabel()
        self.slideshow_info.setStyleSheet("""
            font-size: 12pt;
            font-weight: bold;
            color: #2980b9;
        """)

        title_layout.addWidget(slideshow_title)
        title_layout.addStretch()
        title_layout.addWidget(self.slideshow_info)
        header_layout.addLayout(title_layout)

        # Help text
        self.slideshow_help = QLabel(tr["slideshow_help"])
        self.slideshow_help.setWordWrap(True)
        self.slideshow_help.setStyleSheet("color: #7f8c8d; font-style: italic; font-size: 9pt;")
        header_layout.addWidget(self.slideshow_help)

        layout.addWidget(header_frame)

        # Main content area with image and controls
        content_splitter = QSplitter(Qt.Horizontal)

        # Image display area
        image_frame = QFrame()
        image_frame.setStyleSheet("""
            QFrame {
                border: 2px solid #2980b9;
                border-radius: 8px;
                background-color: white;
                margin: 5px;
            }
        """)
        image_layout = QVBoxLayout(image_frame)

        # Image label
        self.slideshow_image_label = QLabel()
        self.slideshow_image_label.setAlignment(Qt.AlignCenter)
        self.slideshow_image_label.setMinimumHeight(400)
        self.slideshow_image_label.setScaledContents(False)  # Preserve aspect ratio
        self.slideshow_image_label.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.slideshow_image_label.setStyleSheet("""
            QLabel {
                border: 3px solid #bdc3c7;
                border-radius: 10px;
                background-color: #f8f9fa;
                margin: 10px;
            }
        """)
        image_layout.addWidget(self.slideshow_image_label)

        content_splitter.addWidget(image_frame)

        # Controls panel (similar to main window)
        controls_frame = QFrame()
        controls_frame.setMaximumWidth(300)
        controls_frame.setMinimumWidth(250)
        controls_frame.setStyleSheet("""
            QFrame {
                border: 2px solid #2980b9;
                border-radius: 8px;
                background-color: white;
                margin: 5px;
            }
        """)
        controls_layout = QVBoxLayout(controls_frame)
        controls_layout.setSpacing(15)

        # Mirror controls group
        mirror_group = QFrame()
        mirror_group.setStyleSheet("""
            QFrame {
                border: 1px solid #bdc3c7;
                border-radius: 5px;
                background-color: #f8f9fa;
                padding: 10px;
            }
        """)
        mirror_group_layout = QVBoxLayout(mirror_group)

        mirror_title = QLabel("🪞 Mirror Controls")
        mirror_title.setStyleSheet("font-weight: bold; color: #2980b9; font-size: 11pt;")
        mirror_group_layout.addWidget(mirror_title)

        self.slideshow_mirror_h = QCheckBox()
        self.slideshow_mirror_v = QCheckBox()
        self.slideshow_mirror_h.stateChanged.connect(self.update_slideshow_image)
        self.slideshow_mirror_v.stateChanged.connect(self.update_slideshow_image)

        mirror_group_layout.addWidget(self.slideshow_mirror_h)
        mirror_group_layout.addWidget(self.slideshow_mirror_v)

        # Apply to all button
        self.btn_apply_to_all = QPushButton(tr["apply_to_all"])
        self.btn_apply_to_all.clicked.connect(self.apply_mirrors_to_all)
        mirror_group_layout.addWidget(self.btn_apply_to_all)

        controls_layout.addWidget(mirror_group)

        # Navigation controls group
        nav_group = QFrame()
        nav_group.setStyleSheet("""
            QFrame {
                border: 1px solid #bdc3c7;
                border-radius: 5px;
                background-color: #f8f9fa;
                padding: 10px;
            }
        """)
        nav_group_layout = QVBoxLayout(nav_group)

        nav_title = QLabel("🎬 Navigation")
        nav_title.setStyleSheet("font-weight: bold; color: #2980b9; font-size: 11pt;")
        nav_group_layout.addWidget(nav_title)

        self.btn_previous = QPushButton(tr["previous_image"])
        self.btn_next = QPushButton(tr["next_image"])
        self.btn_close_slideshow = QPushButton("❌ Close")

        self.btn_previous.clicked.connect(self.previous_image)
        self.btn_next.clicked.connect(self.next_image)
        self.btn_close_slideshow.clicked.connect(self.close_slideshow)

        nav_group_layout.addWidget(self.btn_previous)
        nav_group_layout.addWidget(self.btn_next)
        nav_group_layout.addWidget(self.btn_close_slideshow)

        controls_layout.addWidget(nav_group)
        controls_layout.addStretch()  # Push controls to top

        content_splitter.addWidget(controls_frame)
        content_splitter.setSizes([800, 300])  # Give more space to image

        layout.addWidget(content_splitter)

        # Update texts and show first image
        self.update_slideshow_texts()
        self.update_slideshow_image()
        self.slideshow_widget.show()

    def update_slideshow_texts(self):
        """Update slideshow interface texts"""
        tr = self.languages[self.current_lang]

        if hasattr(self, 'slideshow_widget') and self.slideshow_widget:
            self.slideshow_widget.setWindowTitle(tr["slideshow_mode"])

            # Update help text if it exists
            if hasattr(self, 'slideshow_help'):
                self.slideshow_help.setText(tr["slideshow_help"])

            # Update mirror controls
            if hasattr(self, 'slideshow_mirror_h'):
                self.slideshow_mirror_h.setText(tr["mirror_horizontal"])
                self.slideshow_mirror_h.setToolTip(tr["mirror_horizontal_tooltip"])
            if hasattr(self, 'slideshow_mirror_v'):
                self.slideshow_mirror_v.setText(tr["mirror_vertical"])
                self.slideshow_mirror_v.setToolTip(tr["mirror_vertical_tooltip"])

            # Update buttons
            if hasattr(self, 'btn_apply_to_all'):
                self.btn_apply_to_all.setText(tr["apply_to_all"])
            if hasattr(self, 'btn_previous'):
                self.btn_previous.setText(tr["previous_image"])
            if hasattr(self, 'btn_next'):
                self.btn_next.setText(tr["next_image"])

            # Update navigation info
            if hasattr(self, 'slideshow_info') and self.batch_results:
                current = self.current_batch_index + 1
                total = len(self.batch_results)
                filename = os.path.basename(self.batch_results[self.current_batch_index]['filename'])
                self.slideshow_info.setText(f"{current}/{total} - {filename}")

    def update_slideshow_image(self):
        """Update the slideshow image with current mirrors applied"""
        if not self.batch_results or self.current_batch_index >= len(self.batch_results):
            return

        current_result = self.batch_results[self.current_batch_index]
        img = current_result['processed_image'].copy()

        # Apply mirrors based on checkbox states
        if hasattr(self, 'slideshow_mirror_h') and self.slideshow_mirror_h.isChecked():
            img = cv2.flip(img, 1)  # Horizontal flip
        if hasattr(self, 'slideshow_mirror_v') and self.slideshow_mirror_v.isChecked():
            img = cv2.flip(img, 0)  # Vertical flip

        # Convert to QPixmap and display
        h, w, ch = img.shape
        bytes_per_line = ch * w
        rgb_image = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
        q_img = QImage(rgb_image.data.tobytes(), w, h, bytes_per_line, QImage.Format_RGB888)
        pixmap = QPixmap.fromImage(q_img)

        # Scale to fit the label with adaptive scaling based on image shape
        available_size = self.slideshow_image_label.size()
        image_size = pixmap.size()

        # Calculate optimal scaling based on image aspect ratio
        image_aspect = image_size.width() / image_size.height()
        display_aspect = available_size.width() / available_size.height()

        # Scale to fit while preserving aspect ratio and image orientation
        if image_aspect > display_aspect:
            # Image is wider - fit to width
            target_width = min(available_size.width() - 20, image_size.width())
            target_height = int(target_width / image_aspect)
        else:
            # Image is taller - fit to height
            target_height = min(available_size.height() - 20, image_size.height())
            target_width = int(target_height * image_aspect)

        scaled_pixmap = pixmap.scaled(target_width, target_height, Qt.KeepAspectRatio, Qt.SmoothTransformation)
        self.slideshow_image_label.setPixmap(scaled_pixmap)

        # Update mirror state in the result
        current_result['mirror_horizontal'] = self.slideshow_mirror_h.isChecked() if hasattr(self, 'slideshow_mirror_h') else True

        # Update navigation info
        self.update_slideshow_texts()

    def previous_image(self):
        """Navigate to previous image in slideshow"""
        if self.current_batch_index > 0:
            self.current_batch_index -= 1
            self.load_slideshow_image()

    def next_image(self):
        """Navigate to next image in slideshow"""
        if self.current_batch_index < len(self.batch_results) - 1:
            self.current_batch_index += 1
            self.load_slideshow_image()

    def load_slideshow_image(self):
        """Load current image in slideshow with its mirror settings"""
        if not self.batch_results or self.current_batch_index >= len(self.batch_results):
            return

        current_result = self.batch_results[self.current_batch_index]

        # Set mirror checkboxes to current image's settings
        if hasattr(self, 'slideshow_mirror_h'):
            self.slideshow_mirror_h.setChecked(current_result.get('mirror_horizontal', True))
        if hasattr(self, 'slideshow_mirror_v'):
            self.slideshow_mirror_v.setChecked(current_result.get('mirror_vertical', False))

        # Update the display
        self.update_slideshow_image()

    def apply_mirrors_to_all(self):
        """Apply current mirror settings to all remaining images"""
        if not self.batch_results:
            return

        current_h = self.slideshow_mirror_h.isChecked() if hasattr(self, 'slideshow_mirror_h') else True
        current_v = self.slideshow_mirror_v.isChecked() if hasattr(self, 'slideshow_mirror_v') else False

        # Apply to all images from current index onwards
        for i in range(self.current_batch_index, len(self.batch_results)):
            self.batch_results[i]['mirror_horizontal'] = current_h
            self.batch_results[i]['mirror_vertical'] = current_v

        tr = self.languages[self.current_lang]
        remaining = len(self.batch_results) - self.current_batch_index
        QMessageBox.information(self, tr["success"], f"Mirror settings applied to {remaining} images")

    def close_slideshow(self):
        """Close the slideshow widget"""
        if hasattr(self, 'slideshow_widget') and self.slideshow_widget:
            self.slideshow_widget.close()
            self.slideshow_widget = None
        self.slideshow_mode = False

    def export_batch_zip(self):
        """Export all batch results as a ZIP file"""
        if not self.batch_results:
            return

        tr = self.languages[self.current_lang]

        # Get save location
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        default_filename = f"dental_analysis_batch_{timestamp}.zip"

        file_path, _ = QFileDialog.getSaveFileName(
            self,
            tr["export_zip"],
            default_filename,
            "ZIP Files (*.zip);;All Files (*)"
        )

        if file_path:
            try:
                # Show progress dialog
                progress_dialog = QProgressDialog(tr["exporting_progress"], "Cancel", 0, len(self.batch_results), self)
                progress_dialog.setWindowModality(Qt.WindowModal)
                progress_dialog.setAutoClose(True)
                progress_dialog.setAutoReset(True)
                progress_dialog.show()

                # Keep track of used filenames to avoid duplicates
                used_filenames = set()

                with zipfile.ZipFile(file_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                    for i, result in enumerate(self.batch_results):
                        # Update progress dialog
                        progress_dialog.setValue(i)
                        progress_dialog.setLabelText(f"{tr['exporting_progress']} ({i+1}/{len(self.batch_results)})")
                        QApplication.processEvents()  # Allow GUI to update

                        # Check if user cancelled
                        if progress_dialog.wasCanceled():
                            self.log_message("❌ Export cancelled by user")
                            return

                        # Apply horizontal mirror to the image
                        img = result['processed_image'].copy()

                        if result.get('mirror_horizontal', True):
                            img = cv2.flip(img, 1)  # Horizontal flip

                        # Create unique filename
                        original_name = os.path.splitext(result['filename'])[0]
                        base_filename = f"{original_name}_processed"
                        export_filename = f"{base_filename}.png"

                        # Handle duplicate filenames
                        counter = 1
                        while export_filename in used_filenames:
                            export_filename = f"{base_filename}_{counter:03d}.png"
                            counter += 1

                        used_filenames.add(export_filename)

                        # Save image to temporary location with unique name
                        temp_path = f"temp_export_{i}_{timestamp}.png"
                        cv2.imwrite(temp_path, img)

                        # Add to ZIP with unique filename
                        zipf.write(temp_path, export_filename)

                        # Clean up temporary file
                        try:
                            os.remove(temp_path)
                        except:
                            pass

                # Close progress dialog
                progress_dialog.close()

                # Show success message with file count
                total_files = len(self.batch_results)
                success_msg = f"{tr['batch_export_success']}\n{file_path}\n\n{total_files} {tr['files_exported']}"
                QMessageBox.information(self, tr["success"], success_msg)
                self.log_message(f"📦 {tr['batch_export_success']} {file_path} ({total_files} {tr['files_exported']})")

            except Exception as e:
                QMessageBox.critical(self, tr["error"], f"{tr['export_failed']} {str(e)}")
                self.log_message(f"❌ {tr['export_failed']} {str(e)}")

                # Clean up any remaining temporary files
                try:
                    for i in range(len(self.batch_results)):
                        temp_path = f"temp_export_{i}_{timestamp}.png"
                        if os.path.exists(temp_path):
                            os.remove(temp_path)
                except:
                    pass

    def keyPressEvent(self, event):
        """Handle keyboard shortcuts for slideshow navigation"""
        if self.slideshow_mode and hasattr(self, 'slideshow_widget') and self.slideshow_widget.isVisible():
            if event.key() == Qt.Key_Left:
                self.previous_image()
            elif event.key() == Qt.Key_Right:
                self.next_image()
            elif event.key() == Qt.Key_Escape:
                self.close_slideshow()

        super().keyPressEvent(event)

    def closeEvent(self, event):
        """Handle application close event"""
        # Clean up any running threads
        if self.processing_thread and self.processing_thread.isRunning():
            self.processing_thread.quit()
            self.processing_thread.wait()

        if hasattr(self, 'batch_processing_thread') and self.batch_processing_thread and self.batch_processing_thread.isRunning():
            self.batch_processing_thread.quit()
            self.batch_processing_thread.wait()

        # Close slideshow if open
        if hasattr(self, 'slideshow_widget') and self.slideshow_widget:
            self.slideshow_widget.close()

        event.accept()

if __name__ == '__main__':
    app = QApplication(sys.argv)
    
    # Set application properties
    app.setApplicationName("Medical Dental Analysis System")
    app.setApplicationVersion("2.0")
    app.setOrganizationName("Medical Imaging Solutions")
    
    # Create and show the main window
    win = MedicalTeethApp()
    win.show()
    
    sys.exit(app.exec_())